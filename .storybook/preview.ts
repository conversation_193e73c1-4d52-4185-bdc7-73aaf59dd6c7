import Toast from "vue-toastification";
import SetupCalendar from "v-calendar";
import FloatingVue, { Tooltip } from "floating-vue";
import vSelect from "vue-select";
import { vClickOutside, vFocus, vIndex, vMask, vRipple } from "@/directives";
import type { Preview } from "@storybook/vue3";
import { setup } from "@storybook/vue3";
import "@/assets/scss/app.scss";
import "simplebar-vue/dist/simplebar.min.css";
import "vue-toastification/dist/index.css";
import "floating-vue/dist/style.css";
import "vue-skeletor/dist/vue-skeletor.css";
import { createPinia } from "pinia";
import { i18nInstance } from "@/libs/i18n";


setup((app) => {
  app.directive("ripple", vRipple);
  app.directive("click-outside", vClickOutside);
  app.directive("mask", vMask);
  app.directive("focus", vFocus);
  app.directive("index", vIndex);
  app.component("v-select", vSelect);
  app.component("VTooltip", Tooltip);
  app.use(FloatingVue, {
    themes: {
      "ui-popper-dropdown": {
        $resetCss: true,
        triggers: ["click"],
        autoHide: true,
        placement: "bottom",
      },
      "ui2-dropdown-list": {
        $resetCss: true,
        triggers: ["click"],
        autoHide: true,
        placement: "bottom",
      },
      "ui-popper-dropdown-black": {
        triggers: ["click"],
        $resetCss: true,
        autoHide: true,
        placement: "bottom",
      },
      "ui-tooltip": {
        $resetCss: true,
        triggers: ["hover", "click"],
        autoHide: true,
        placement: "bottom",
      },
    },
  });
  app.use(SetupCalendar);
  app.use(Toast, {
    shareAppContext: true,
    containerClassName: "ui-toast__container",
    transition: "Vue-Toastification__fade",
  });
  app.use(i18nInstance);
  app.use(createPinia());
});

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
};

export default preview;
