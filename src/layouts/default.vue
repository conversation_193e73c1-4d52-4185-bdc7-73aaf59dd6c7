<script lang="ts" setup>
import AsideMain from "@/components/Layout/Aside/AsideMain.vue";
import Toolbar from "@/components/Layout/Toolbar/Toolbar.vue";
import UIMobileBottomBar from "@/components/ui/UIMobileBottomBar.vue";
import { computed, onBeforeUnmount, onMounted, ref } from "vue";
import { useRoute, useRouter } from "vue-router";
import { useUserStore } from "@/stores/user";
import { useCardsStore } from "@/stores/cards";
import { namespaces } from "@/config/stores";
import { useTracker } from "@/composable";
import { LocalStorageKey } from "@/constants/local_storage_key";
import { LAYOUT_CONTENT_SCROLLBOX_ID } from "@/constants/layout_content_scrollbox_id";
import { useNotifications } from "@/stores/notifications";
import Snackbars from "@/components/Widgets/Snackbars/Snackbars.vue";
import AsideShort from "@/components/Layout/Aside/mobile/AsideShort.vue";
import { useElementSize } from "@vueuse/core";
import { isLaptopOrDesktop, isMobile } from "@/helpers";
import { RouteName } from "@/constants/route_name";
import { initIntercom } from "@/libs/intercom";

const route = useRoute();
const router = useRouter();
const layoutScrollboxId = LAYOUT_CONTENT_SCROLLBOX_ID;

const notificationsStore = useNotifications();
const { state, getCards } = useCardsStore(namespaces.allUserCards);
const userStore = useUserStore();
const tracker = useTracker();

const snackbarRef = ref<HTMLElement | null>(null);
const snackbarSize = useElementSize(snackbarRef);

// methods
const getUserJoinPrivateKey = () =>
  `${LocalStorageKey.SENT_JOIN_PRIVATE}${userStore.userKey}`;

// computed
const isAllowToShowJoinPrivateModal = computed<boolean>(
  () => !localStorage.getItem(getUserJoinPrivateKey())
);

// setup
notificationsStore.getAll();
userStore.updateActualKycLevel();
if (!state.loading && state.total === 0) {
  getCards({ loadAllCards: true });
}

// hooks
onMounted(() => {
  document.body.classList.add("l-default");
  if (userStore.user?.uuid) {
    tracker.setUserId(userStore.user?.uuid);
    // @ts-expect-error wrong type in userStore
    initIntercom(userStore.user);
  }

  if (route.query.p && isAllowToShowJoinPrivateModal) {
    router.push({ name: RouteName.SUBSCRIPTION_PROMO });
    return;
  }

  userStore.updateUserSummary();
});

onBeforeUnmount(() => {
  notificationsStore.reset();
  document.body.classList.remove("l-default");
});
</script>

<template>
  <div class="root">
    <div class="page">
      <Snackbars
        ref="snackbarRef"
        class="snackbar" />
      <Toolbar class="toolbar" />
      <div class="sidebar flex flex-col">
        <AsideMain
          v-if="isLaptopOrDesktop"
          class="flex flex-col"
          :snackbar-height="snackbarSize.height.value" />
        <AsideShort
          v-else-if="!isMobile"
          :snackbar-height="snackbarSize.height.value" />
      </div>

      <div
        :id="layoutScrollboxId"
        class="content">
        <main class="max-w-[1190px]">
          <slot />
        </main>
      </div>

      <UIMobileBottomBar class="bottom-bar" />
    </div>

    <div
      id="modals"
      class="modal" />
    <PortalTarget
      class="modal"
      multiple
      name="modals" />
  </div>
</template>

<style lang="scss" scoped>
.page {
  @apply grid overflow-y-hidden h-dvh;

  grid-template-rows: auto auto 1fr;
  grid-template-columns: 1fr;
  grid-template-areas:
    "toolbar"
    "snackbar"
    "content"
    "bottom-bar";

  @screen sm {
    grid-template-columns: 63px 1fr;
    grid-template-areas:
      "snackbar snackbar"
      "sidebar toolbar"
      "sidebar content";
  }

  @screen lg {
    grid-template-columns: 250px 1fr;
    grid-template-areas:
      "snackbar snackbar"
      "toolbar toolbar"
      "sidebar content";
  }

  @screen xxl {
    grid-template-columns: 1fr 250px 1190px 1fr;
    grid-template-areas:
      "snackbar snackbar snackbar snackbar"
      ". toolbar toolbar ."
      ". sidebar content content";
  }
}

.toolbar {
  grid-area: toolbar;
}

.bottom-bar {
  grid-area: bottom-bar;
}

.root {
  @apply min-h-dvh relative;
}

.snackbar {
  grid-area: snackbar;
}

.sidebar {
  grid-area: sidebar;
}

.content {
  grid-area: content;
  overflow-y: auto;
}

.modal {
  @apply absolute z-6;
}
</style>
