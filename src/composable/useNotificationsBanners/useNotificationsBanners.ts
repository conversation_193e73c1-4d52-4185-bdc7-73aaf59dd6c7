import { detectExtension, isChromium } from "@/helpers/extension";
import { type TNotificationBanner, TBannerSlug } from "./types";
import { useSocialsBotConnection } from "@/composable/useSocialsBotConnection";
import {
  useBusinessMembersTransactionsV2Get,
  useTransactionsV2Get,
  useUserSecuritySettings,
} from "@/composable";
import { useNotificationSettings } from "@/components/SettingsNotifications/useNotificationSettings";
import { computed, onMounted, ref } from "vue";
import { useUserStore } from "@/stores/user";
import { TRANSACTIONS_REQ_BODY } from "@/composable/useNotificationsBanners/constants";
import OneSignalService from "@/services/OneSignalService";
import {
  ERemoteStorageKeys,
  RemoteStorageService,
} from "@modules/services/remoteStorage";

export const useNotificationsBanners = () => {
  const {
    whatsappLink,
    telegramLink,
    isConnected,
    isFetching: isLoadingBotConnection,
  } = useSocialsBotConnection();
  const { userSecuritySettings } = useUserSecuritySettings();

  // DO NOT DELETE: temporary disabled PST-T-5783
  // const { data: sessionsData, isFetching: isLoadingSessions } = useTokenGet({
  //   page: 1,
  //   per_page: 100,
  // });

  const { isWebPushEnabled } = useNotificationSettings();
  const isExtensionInstalled = ref(false);
  const { isTeamOwner } = useUserStore();

  const notificationBanners = ref<TBannerSlug[]>([]);

  RemoteStorageService.get(ERemoteStorageKeys.NOTIFICATION_BANNERS).then(
    (value) => {
      if (value) {
        notificationBanners.value = value;
        return;
      } else {
        notificationBanners.value = [
          // TBannerSlug.APP,
          TBannerSlug.FEEDBACK,
          TBannerSlug.BOTS,
          TBannerSlug.SOCIALS,
          TBannerSlug.EXTENSION,
          TBannerSlug.WEBPUSH,
        ];
      }
    }
  );

  const bannerClicked = async (banner: TBannerSlug) => {
    notificationBanners.value = notificationBanners.value.filter(
      (nb) => nb !== banner
    );

    await RemoteStorageService.set(
      ERemoteStorageKeys.NOTIFICATION_BANNERS,
      notificationBanners.value
    );
  };

  const { data: payments, isFetching: isLoadingPayments } = isTeamOwner
    ? useBusinessMembersTransactionsV2Get(TRANSACTIONS_REQ_BODY)
    : useTransactionsV2Get(TRANSACTIONS_REQ_BODY);

  const availableBanners = computed<TNotificationBanner[]>(() => {
    return bannerConfig
      .filter((item) => item.condition())
      .map((item) => {
        return {
          type: item.type,
          read: !notificationBanners.value.includes(item.type),
        };
      });
  });

  const bannerConfig: { type: TBannerSlug; condition: () => boolean }[] = [
    // DO NOT DELETE: temporary disabled PST-T-5783
    // {
    //   type: TBannerSlug.APP,
    //   condition: () =>
    //     !sessionsData.value?.data?.find((item) =>
    //       isMobileAppSession(item.user_agent)
    //     ),
    // },
    {
      type: TBannerSlug.FEEDBACK,
      condition: () => Boolean(payments.value?.data?.length),
    },
    { type: TBannerSlug.BOTS, condition: () => !isConnected.value },
    { type: TBannerSlug.SOCIALS, condition: () => true },
    {
      type: TBannerSlug.EXTENSION,
      condition: () => isChromium && !isExtensionInstalled.value,
    },
    { type: TBannerSlug.WEBPUSH, condition: () => !isWebPushEnabled.value },
  ];

  const isBannersLoading = computed(() => {
    return (
      isLoadingBotConnection.value && isLoadingPayments.value
      // && isLoadingSessions.value
    );
  });

  const connectToWebPush = () => {
    OneSignalService.subscribe(userSecuritySettings.value?.onesignal_hash);
  };

  onMounted(() => {
    detectExtension((state) => {
      if (state) {
        isExtensionInstalled.value = true;
      }
    });
  });

  const bannersTotal = computed(() => {
    return availableBanners.value.filter((item) => !item.read).length;
  });

  return {
    availableBanners,
    isBannersLoading,
    whatsappLink,
    telegramLink,
    connectToWebPush,
    bannersTotal,
    bannerClicked,
  };
};
