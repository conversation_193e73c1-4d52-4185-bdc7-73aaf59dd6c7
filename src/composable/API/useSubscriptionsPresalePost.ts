import type { UseFetchOptions } from "@vueuse/core";
import { usePstFetch } from "../usePstFetch";

export type SubscriptionsPresaleReq = {
  subscription_tariff_id: number;
  user_account_id: number;
};

export const useSubscriptionsPresalePost = (
  config: SubscriptionsPresaleReq,
  options: UseFetchOptions = {}
) => {
  return usePstFetch("subscriptions/presale", options)
    .post(config)
    .json<{ success: boolean; message: string }>();
};
