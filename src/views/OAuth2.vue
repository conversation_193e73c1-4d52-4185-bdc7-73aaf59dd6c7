<template>
  <div :class="$style.OAuthPage">
    <div :class="$style.OAuthPage__form">
      <div :class="$style.FormForgotten">
        <div
          v-if="emailInput"
          class="text-h4 lg:text-h3 font-extrabold">
          {{ $t("register") }}
        </div>

        <div
          v-if="emailInput"
          class="w-full">
          <div class="col-span-2 grid grid-cols-2 gap-4">
            <UiInputText
              :value="email.value.value"
              data-cy="oauth_email"
              type="email"
              class="col-span-2"
              :placeholder="$t('email')"
              @change="email.setValue" />

            <UiButton
              data-cy="login_button_login_with_email"
              size="large"
              type="primary"
              :title="$t('sign_in')"
              class="col-span-2 mt-3 mb-4 min-w-full"
              :disabled="isLoading"
              @click="
                () => {
                  if (isWhatsapp) {
                    loginWithWhatsapp($route.query.whatsapp, email.value.value);
                  } else {
                    loginWithTelegram($route.query.tg, email.value.value);
                  }
                }
              " />
          </div>
        </div>

        <div v-else-if="!showError">
          <h3>Loading data</h3>
          <Loader padding="true" />
        </div>

        <div
          v-if="showError"
          class="flex flex-col gap-4">
          <span class="text-error-base">{{ errorMessage }}</span>

          <UiButton
            v-if="isApple || isGoogle"
            :title="$t('buttons.tryAgain')"
            @click="onTryAgain" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import UiInputText from "@/components/ui/InputText/InputText";
import UiButton from "@/components/ui/Button/Button";
import { useEmail } from "@/helpers/validation";
import Loader from "@/components/ui/Loader/Loader";
import { useRoute, useRouter } from "vue-router";
import Auth, { getUtm } from "@/helpers/auth";
import { useAxios } from "@/helpers/axios";
import { computed, ref } from "vue";
import { useTracker, TrackerEvent } from "@/composable";
import { useUserStore } from "@/stores/user";
import * as Sentry from "@sentry/vue";
import { useDictionary } from "@/stores/dictionary";
import { useExperiments } from "@/composable/useExperiments";
import { LocalStorageKey } from "@/constants/local_storage_key";
import { RouteName } from "@/constants/route_name";
import { SessionStorageKey } from "@/constants/session_storage_key";
import { useTwoFactorAuthRedirect } from "@/composable/useTwoFactorAuthRedirect";
import {
  ERemoteStorageKeys,
  RemoteStorageService,
} from "@/modules/services/remoteStorage";

export default {
  components: {
    UiInputText,
    UiButton,
    Loader,
  },
  layout: "login",
  setup() {
    const email = useEmail("email", "");
    const route = useRoute();
    const router = useRouter();
    const showError = ref(false);
    const emailInput = ref(false);
    const firstStart = ref(true);
    const errorMessage = ref("");
    const hash = localStorage.getItem(LocalStorageKey.REF_HASH);
    const isFormPrivate = localStorage.getItem(LocalStorageKey.FORM_PRIVATE);
    const isLoading = ref(false);
    const { redirectTo2FA } = useTwoFactorAuthRedirect();
    const tracker = useTracker();

    /** Set logEvent & localStorage item if new landing experiment active */
    const logNewLandingExperimentEvent = () => {
      const isNewLandingExperiment = localStorage.getItem(
        LocalStorageKey.NEW_PST_LANDING_EXPERIMENT
      );

      if (isNewLandingExperiment) {
        tracker.logEvent(TrackerEvent.NEW_REGISTRATION_NEWPST);
        RemoteStorageService.set(ERemoteStorageKeys.NEW_PST_LANDING, 1);
      }
    };

    const master_hash =
      sessionStorage.getItem(SessionStorageKey.MASTER_HASH) || undefined;

    const pushArgsAfterRegister = {
      path: "/app",
    };

    if (isFormPrivate === "true") {
      pushArgsAfterRegister.query = {
        p: true,
      };
    }

    const delHash = () => {
      localStorage.removeItem(LocalStorageKey.REF_HASH);
      localStorage.removeItem(LocalStorageKey.FORM_PRIVATE);
    };

    const isTelegram = computed(() => route.query.tg !== undefined);
    const isWhatsapp = computed(() => route.query.whatsapp !== undefined);
    const isApple = computed(() => route.query.type === "apple");
    const isGoogle = computed(() => route.query.code !== undefined);

    const authType = computed(() => {
      if (isApple.value) {
        return "apple";
      }
      if (isTelegram.value) {
        return "telegram";
      }
      if (isWhatsapp.value) {
        return "whatsapp";
      }
      return "google";
    });

    Sentry.addBreadcrumb({
      category: "oauth",
      message: `Oauth by ${authType.value}`,
      level: "info",
    });

    const { initGeoBlock, initTwoStepVerificationExperiment } =
      useExperiments();
    // const { initNewUltimaFlowExperiment } = useNewUltimaFlowExperiment();

    const checkGeoBlock = async () => {
      const value = await initGeoBlock();
      if (value) {
        await router.push({ name: RouteName.UNAVAILABLE_COUNTRIES });
      }
    };

    // login tg
    const loginWithTelegram = (tgCode = "", email = "") => {
      isLoading.value = true;
      Auth.loginWithTelegram({ data: tgCode, email, hash, master_hash })
        .then(async (res) => {
          if (res.status) {
            if (res.data.message === "Incorrect link") {
              errorMessage.value = "Your auth link has expired!";
              showError.value = true;
              return false;
            }
            if (res.data.message === "User already exists") {
              errorMessage.value = res.data.message;
              showError.value = true;
              return false;
            }
            if (emailInput.value) {
              const utm = getUtm();
              const { getUser, user } = useUserStore();
              await getUser({ params: utm });

              if (user?.uuid) {
                tracker.setUserId(user.uuid);
              }
              tracker.logEvent(TrackerEvent.ACCOUNT_CREATED, {
                "account type": "telegram",
              });
              logNewLandingExperimentEvent();

              // Initialization of experiments
              await Promise.all([
                initTwoStepVerificationExperiment(),
                checkGeoBlock(),
                // initNewUltimaFlowExperiment(),
              ]);

              await router.push(pushArgsAfterRegister);
            } else {
              await router.push(pushArgsAfterRegister);
            }
          } else {
            showError.value = true;
          }
        })
        .catch((res) => {
          Sentry.captureException(res);
          const response = res.response;

          if (response.data.message === "Error email") {
            if (firstStart.value) {
              firstStart.value = false;
              emailInput.value = true;
              return false;
            } else {
              emailInput.value = true;
              errorMessage.value = "Please, enter your email address!";
              showError.value = true;
              return false;
            }
          }

          if (response.data.message === "Incorrect link") {
            errorMessage.value = "Your auth link has expired!";
            showError.value = true;
            return false;
          }
          if (response.data.message === "User already exists") {
            errorMessage.value = res.data.message;
            showError.value = true;
            return false;
          }

          showError.value = true;
          errorMessage.value = response?.data?.message;
        })
        .finally(() => {
          delHash();
          isLoading.value = false;
        });
    };

    const loginWithWhatsapp = async (whatsappCode = "", email = "") => {
      isLoading.value = true;
      Auth.loginWithWhatsapp({ data: whatsappCode, email, hash, master_hash })
        .then(async (res) => {
          if (res.status) {
            if (res.data.message === "Incorrect link") {
              errorMessage.value = "Your auth link has expired!";
              showError.value = true;
              return false;
            }
            if (res.data.message === "User already exists") {
              errorMessage.value = res.data.message;
              showError.value = true;
              return false;
            }
            if (emailInput.value) {
              const utm = getUtm();
              const { getUser, user } = useUserStore();
              await getUser({ params: utm });

              if (user?.uuid) {
                await tracker.setUserId(user.uuid);
              }
              await tracker.logEvent(TrackerEvent.ACCOUNT_CREATED, {
                "account type": "whatsapp",
              });
              logNewLandingExperimentEvent();

              // Initialization of experiments
              await Promise.all([
                initTwoStepVerificationExperiment(),
                checkGeoBlock(),
              ]);
            }
            await router.push(pushArgsAfterRegister);
          } else {
            showError.value = true;
          }
        })
        .catch((res) => {
          Sentry.captureException(res);
          const response = res.response;

          if (response.data.message === "Error email") {
            if (firstStart.value) {
              firstStart.value = false;
              emailInput.value = true;
              return false;
            } else {
              emailInput.value = true;
              errorMessage.value = "Please, enter your email address!";
              showError.value = true;
              return false;
            }
          }

          if (response.data.message === "Incorrect link") {
            errorMessage.value = "Your auth link has expired!";
            showError.value = true;
            return false;
          }
          if (response.data.message === "User already exists") {
            errorMessage.value = res.data.message;
            showError.value = true;
            return false;
          }

          showError.value = true;
          errorMessage.value = response?.data?.message;
        })
        .finally(() => {
          delHash();
          isLoading.value = false;
        });
    };

    const sendRegisterAnalytics = async (email) => {
      setTimeout(async () => {
        const { getUser, user } = useUserStore();
        await getUser();
        await tracker.logEvent("user id updated", {
          "account type": authType.value,
        });
      }, 1000);

      await tracker.logEvent(TrackerEvent.ACCOUNT_CREATED, {
        "account type": authType.value,
      });

      await tracker.flush();
    };

    const loginWithGoogle = () =>
      Auth.loginWithGoogle(String(route.query.code), hash, master_hash)
        .then(async (res) => {
          if (!res.status) {
            showError.value = true;
            const error = res.error;

            if (error.status === 400) {
              Sentry.addBreadcrumb({
                category: "oauth",
                message: "logout reason: Account already exists",
                level: "info",
              });
              await router.push("/login");
            } else if (error.status === 500) {
              Sentry.addBreadcrumb({
                category: "oauth",
                message: "logout reason: api returns 500",
                level: "info",
              });
              await router.push("/login");
            }
          } else {
            const { getUser, user } = useUserStore();
            if (res.data.data.type !== "login") {
              try {
                Promise.all([
                  sendRegisterAnalytics(user.email),
                  initTwoStepVerificationExperiment(),
                  checkGeoBlock(),
                ]);
              } catch (e) {
                console.error("OAuth2->loginWithGoogle error handled: ", e);
                sendRegisterAnalytics(user.email).catch((e) => {
                  console.error(
                    "OAuth2->sendRegisterAnalytics error handled: Can`t send analytics report (second try)",
                    e
                  );
                });
              }
              await router.push(pushArgsAfterRegister);
            } else {
              // check 2fa for social auth
              const authId = res.data.data.auth_id;
              if (authId) {
                await redirectTo2FA(authId);

                return;
              }
              // get auth_id
              if (user?.uuid) {
                await tracker.setUserId(user.uuid);
              }
              await tracker.logEvent(TrackerEvent.LOGGED_IN, {
                "account type": "google",
              });
              logNewLandingExperimentEvent();
              const utm = getUtm();
              await getUser({ params: utm });

              await router.push(pushArgsAfterRegister);
            }
          }
        })
        .catch((res) => {
          Sentry.captureException(res);
          const response = res.response;

          showError.value = true;
          errorMessage.value = response?.data?.message;
        })
        .finally(delHash);

    const loginWithApple = () =>
      Auth.loginWithApple(String(route.query.code), hash, master_hash)
        .then(async (res) => {
          if (!res.status) {
            showError.value = true;
            const error = res.error;

            if (error.status === 400) {
              Sentry.addBreadcrumb({
                category: "oauth",
                message: "logout reason: Account already exists",
                level: "info",
              });
              await router.push("/login");
            } else if (error.status === 500) {
              setTimeout(() => {
                Sentry.addBreadcrumb({
                  category: "oauth",
                  message: "logout reason: api returns 500",
                  level: "info",
                });
                router.push("/login");
              }, 1000);
            }
          } else {
            const { user, getUser } = useUserStore();

            if (res.data.data.type !== "login") {
              // if (user?.email) {
              try {
                await Promise.all([
                  sendRegisterAnalytics(user.email),
                  initTwoStepVerificationExperiment(),
                  checkGeoBlock(),
                ]);
              } catch (e) {
                console.error("OAuth2->loginWithGoogle error handled: ", e);
                sendRegisterAnalytics(user.email).catch((e) => {
                  console.error(
                    "OAuth2->sendRegisterAnalytics error handled: Can`t send analytics report (second try)",
                    e
                  );
                });
              }

              await router.push(pushArgsAfterRegister);
            } else {
              // check 2fa for social auth
              const authId = res.data.data.auth_id;
              if (authId) {
                await redirectTo2FA(authId);

                return;
              }
              if (user?.uuid) {
                await tracker.setUserId(user.uuid);
              }
              await tracker.logEvent(TrackerEvent.LOGGED_IN, {
                "account type": "apple",
              });
              logNewLandingExperimentEvent();

              const utm = getUtm();
              await getUser({ params: utm });
              await router.push(pushArgsAfterRegister);
            }
          }
        })
        .catch((res) => {
          Sentry.captureException(res);
          const response = res.response;

          showError.value = true;
          errorMessage.value = response?.data?.message;
        })
        .finally(delHash);

    return {
      isLoading,
      email: email,
      showError,
      isTelegram,
      isWhatsapp,
      isApple,
      isGoogle,
      authType,
      emailInput,
      errorMessage,
      tgCode: route.query.tg,
      whatsappCode: route.query.whatsapp,
      loginWithTelegram,
      loginWithWhatsapp,
      loginWithGoogle,
      loginWithApple,
    };
  },
  data() {
    return {
      data: "",
      error: "",
      googleLink: "",
      googleId: "",
    };
  },
  mounted() {
    if (this.$route.query.code !== undefined) {
      if (this.$route.query.type === "apple") {
        this.contextLoginWithApple();
      } else {
        this.contextLoginWithGoogle();
      }
    } else if (this.$route.query.tg !== undefined) {
      this.contextLoginWithTelegram();
    } else if (this.$route.query.whatsapp !== undefined) {
      this.contextLoginWithWhatsapp();
    } else {
      Sentry.addBreadcrumb({
        category: "oauth",
        message: "logout reason: Page opened without query",
        level: "info",
      });
      this.$router.push("/login");
    }

    /** Check if user comes from new (experimental) PST landing */
    if (this.$route.query.n_pst_l_ex === "1") {
      localStorage.setItem(LocalStorageKey.NEW_PST_LANDING_EXPERIMENT, "true");
    }
  },
  methods: {
    contextLoginWithGoogle() {
      return this.loginWithGoogle();
    },
    contextLoginWithTelegram() {
      return this.loginWithTelegram(this.tgCode, "");
    },
    contextLoginWithWhatsapp() {
      return this.loginWithWhatsapp(this.whatsappCode, "");
    },
    contextLoginWithApple() {
      return this.loginWithApple();
    },
    async onTryAgain() {
      try {
        let response;
        const dictionaryStore = useDictionary();
        if (this.authType === "whatsapp") {
          window.location.href =
            "https://wa.me/" +
            (dictionaryStore.dictionary?.oauth?.whatsapp_bot || "") +
            "?text=/start";
        }
        if (this.authType === "telegram") {
          window.location.href =
            "https://t.me/" +
            (dictionaryStore.dictionary?.oauth?.bot_name || "");
        }
        if (this.authType === "apple") {
          response = await useAxios().get("/oauth/apple/link");
          window.location.href = response.data?.data?.apple_url;
        }
        if (this.authType === "google") {
          response = await useAxios().get("/oauth/google/link");
          window.location.href = response.data?.data?.google_url;
        }
      } catch (ex) {
        console.error("OAuth2->onTryAgain error handled: ", ex);
      }
    },
  },
};
</script>

<style lang="scss" module>
.OAuthPage {
  @apply relative w-full flex items-center justify-center;

  &__form {
    @apply w-full md:w-[610px] m-auto;
  }
}

.FormForgotten {
  @apply relative py-10 px-4 lg:px-10 bg-white rounded-lg flex flex-col justify-center items-center text-center gap-4;
  box-shadow: 0 16px 24px rgba(93, 106, 131, 0.02);
}
</style>
