<script lang="ts" setup>
import type { TransactionGetReq } from "@/composable/API/useTransactionGet";
import type { TMemberAccountResource } from "@/types/api/TMemberAccountResource";
import { RouteName } from "@/constants/route_name";
import { useRouter } from "vue-router";
import { computed, ref, watch } from "vue";
import { useDictionary } from "@/stores/dictionary";
import AccountListItem from "@/components/Accounts/AccountListItem.vue";
import { useBusinessMembersAccountsIdGet } from "@/composable/API/useBusinessMembersAccountsIdGet";
import { TOAST_TYPE, useCallToast } from "@/composable/useCallToast";
import { Skeletor } from "vue-skeletor";
import UISideModal from "@/components/ui/UISideModal/UISideModal.vue";
import LastPaymentsList from "@/components/LastPaymentsList/LastPaymentsList.vue";
import UITransition from "@/components/ui/UITransition.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { useUserStore } from "@/stores/user";
import type { TIsoCode, TypeExchangeRates } from "@/composable/useAccounts";
import { getCurrencyById } from "@/helpers/store";
import {
  useBusinessMembersCardsGet,
  useBusinessMembersTransactionsGet,
  useCardsGet,
  useModalStack,
} from "@/composable";
import type { MemberTransactionsResource } from "@/types/api/MemberTransactionsResource";
import type { TCardResource } from "@/types/api/TCardResource";
import type { TAccount } from "@/types/TAccount";
import { useTeamModalAccountInfo } from "@/composable/useTeamModalAccountInfo";
import type { TTransferModalProps } from "@/components/TransferModal/types";
import { useI18nWrapper } from "@/composable/useI18nWrapper";

const { t } = useI18nWrapper();
const router = useRouter();
const { isOpen, accountId, close } = useTeamModalAccountInfo();
const { openModal } = useModalStack();

const account = ref<TMemberAccountResource>();
const isoCode = ref<TIsoCode>();

const userStore = useUserStore();

const isLoadingTransactions = ref<boolean>(false);
const isFetchingAccount = ref<boolean>(false);

const memberEmail = computed(() => {
  return account.value?.user_email ?? "";
});

const memberName = computed(() => {
  return account.value?.user_name ?? "";
});

/**
 * Hide for not team owner users
 */
const subtitle = computed(() => {
  if (!userStore.isTeamOwner) {
    return "";
  }
  if (isMyAccount.value) {
    return "Master (You)";
  } else {
    return memberEmail.value ?? "";
  }
});

const isMasterAccount = computed(() => {
  return account.value?.is_master ?? "";
});

const title = computed(() => {
  return `${t("cards.account")} ${
    !isMyAccount.value && memberName.value !== memberEmail.value
      ? memberName.value
      : ""
  }`;
});

const { data: masterCards } = useCardsGet({
  sort: "balance",
  direction: "desc",
});

const { data: memberCards } = useBusinessMembersCardsGet({
  sort: "balance",
  direction: "desc",
});

const isMyAccount = computed(
  () => userStore.user.email === account.value?.user_email
);

const disableAccountToCardsBtn = computed<boolean>(() => {
  const cards = isMasterAccount.value
    ? masterCards.value?.data || []
    : memberCards.value?.data || [];

  if (!cards.length) return true;

  return !cards.some((card: TCardResource) => {
    return (
      (card.simple_status === "inactive" && Number(card.account.balance) < 1) ||
      card.simple_status === "active"
    );
  });
});

const rates = computed<TypeExchangeRates | null>(() => {
  const { dictionary } = useDictionary();
  return dictionary?.exchangeRates || null;
});

const actualAccount = computed(() => {
  if (!account.value) return {};
  const { dictionary } = useDictionary();
  const currencyId = account.value?.currency_id;
  const currency = (dictionary?.currencies || []).find(
    (currency) => currency.id === currencyId
  );
  return {
    ...account.value,
    _currency: currency,
  };
});

const transactions = ref<MemberTransactionsResource[] | undefined>();
const loadTransactions = async () => {
  transactions.value = undefined;
  isLoadingTransactions.value = true;
  const { data } = await useBusinessMembersTransactionsGet({
    direction: "desc",
    account_id: accountId.value,
    sort: "processed_at",
    per_page: 3,
  } as TransactionGetReq);
  if (data.value?.data) {
    transactions.value = data.value?.data?.slice(0, 3) || [];
  }
  isLoadingTransactions.value = false;
};

const loadAccount = async () => {
  isFetchingAccount.value = true;
  account.value = undefined;

  if (!accountId.value) {
    isFetchingAccount.value = false;
    return;
  }

  const { data } = await useBusinessMembersAccountsIdGet(accountId.value);

  if (!data.value?.data) {
    useCallToast({
      title: t("errors.universal-request-error"),
      options: {
        id: "business-members-accounts-id-get",
        type: TOAST_TYPE.ERROR,
      },
    });
    return;
  }

  account.value = data.value?.data;

  const currency = getCurrencyById(account.value?.currency_id);
  isoCode.value = currency?.iso_code as TIsoCode;

  isFetchingAccount.value = false;
};

const loadData = () => {
  loadAccount();
  loadTransactions();
};

const openCardOperations = () => {
  router.push({
    name: RouteName.TEAM_OPERATIONS_BY_ACCOUNT,
    params: {
      accountId: account.value?.id,
    },
  });
  close();
};

type TransferModalType =
  | "depositMaster"
  | "withdrawMaster"
  | "toCardMaster"
  | "depositMember"
  | "withdrawMember"
  | "toCardMember";

const transferOptionsByType = computed(() => {
  return {
    depositMaster: {
      strategy: "userAccountsToUserAccountStrategy",
      toAccountId: accountId.value,
    },
    withdrawMaster: {
      strategy: "userAccountsToUserAccountsStrategy",
      fromAccountId: accountId.value,
    },
    toCardMaster: {
      strategy: "userAccountToUserCardsStrategy",
      fromAccountId: accountId.value,
    },
    depositMember: {
      strategy: "masterAccountsToMemberAccountStrategy",
      toAccountId: accountId.value,
      memberId: account.value?.user_id,
    },
    withdrawMember: {
      strategy: "memberAccountToMasterAccountsStrategy",
      fromAccountId: accountId.value,
      memberId: account.value?.user_id,
    },
    toCardMember: {
      strategy: "memberAccountToMemberCardsStrategy",
      fromAccountId: accountId.value,
      memberId: account.value?.user_id,
    },
  };
});

const openTransferModalByType = (type: TransferModalType) => {
  openModal("transferModal", {
    ...(transferOptionsByType.value[type] as TTransferModalProps),
  });
  close();
};

watch(accountId, () => {
  loadData();
});
</script>

<template>
  <UISideModal
    :is-open="isOpen"
    :title
    :subtitle
    @close="close">
    <template #content>
      <UITransition>
        <div v-if="isFetchingAccount">
          <Skeletor
            class="rounded"
            height="8.625rem"
            width="100%" />

          <div class="flex flex-row justify-between gap-2 mb-2">
            <Skeletor
              class="rounded mt-2 flex flex-auto"
              height="3rem" />
            <Skeletor
              class="rounded mt-2 flex flex-auto"
              height="3rem" />
            <Skeletor
              class="rounded mt-2 flex flex-auto"
              height="3rem" />
          </div>

          <Skeletor
            class="rounded"
            height="10rem"
            width="100%" />
        </div>

        <div
          v-else
          class="flex flex-col gap-10">
          <div class="flex flex-none flex-col items-center">
            <div class="flex flex-none m-auto w-[17.719rem]">
              <AccountListItem
                :account="(actualAccount as TAccount)"
                :rates="rates"
                class="w-full" />
            </div>

            <div
              v-if="!isMasterAccount"
              class="flex s:flex-row flex-col gap-2 mt-7 w-[17.719rem] s:w-full">
              <UIButton
                v-if="!userStore.isTeamMember && !userStore.isSuspicious"
                class="flex flex-auto w-full"
                color="black"
                size="m"
                @click="openTransferModalByType('depositMember')">
                <template #left>
                  <DynamicIcon name="add" />
                </template>
                {{ $t("cards.deposit") }}
              </UIButton>
              <UIButton
                class="flex flex-auto w-full"
                color="black"
                size="m"
                @click="openTransferModalByType('withdrawMember')">
                <template #left>
                  <DynamicIcon name="send-outline" />
                </template>
                {{ $t("withdraw") }}
              </UIButton>
              <UIButton
                :disabled="disableAccountToCardsBtn"
                class="flex flex-auto w-full"
                color="black"
                size="m"
                @click="openTransferModalByType('toCardMember')">
                <template #left>
                  <DynamicIcon name="to-card" />
                </template>
                {{ $t("cards.to-cards-btn") }}
              </UIButton>
            </div>
            <div
              v-if="isMasterAccount"
              class="flex s:flex-row flex-col gap-2 mt-7 w-[17.719rem] s:w-full">
              <UIButton
                v-if="!userStore.isTeamMember"
                class="flex flex-auto w-full"
                color="black"
                size="m"
                @click="openTransferModalByType('depositMaster')">
                <template #left>
                  <DynamicIcon name="add" />
                </template>
                {{ $t("cards.deposit") }}
              </UIButton>
              <UIButton
                class="flex flex-auto w-full"
                color="black"
                size="m"
                @click="openTransferModalByType('withdrawMaster')">
                <template #left>
                  <DynamicIcon name="send-outline" />
                </template>
                {{ $t("withdraw") }}
              </UIButton>
              <UIButton
                :disabled="disableAccountToCardsBtn"
                class="flex flex-auto w-full"
                color="black"
                size="m"
                @click="openTransferModalByType('toCardMaster')">
                <template #left>
                  <DynamicIcon name="to-card" />
                </template>
                {{ $t("cards.to-cards-btn") }}
              </UIButton>
            </div>
          </div>

          <LastPaymentsList
            v-if="transactions && transactions.length"
            :is-loading="isLoadingTransactions"
            :payments="transactions">
            <div class="p-4">
              <UIButton
                class="mt-2 w-full"
                color="white"
                size="s"
                @click="openCardOperations">
                {{ $t("operations.open-btn") }}
              </UIButton>
            </div>
          </LastPaymentsList>
        </div>
      </UITransition>
    </template>
  </UISideModal>
</template>
