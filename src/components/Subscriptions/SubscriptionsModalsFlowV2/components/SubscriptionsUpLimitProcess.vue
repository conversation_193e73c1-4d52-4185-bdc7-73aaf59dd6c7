<script lang="ts" setup>
import { computed, reactive, ref, watch, watchEffect } from "vue";
import { useRouter } from "vue-router";
import { RouteName } from "@/constants/route_name";
import BigNumber from "bignumber.js";
import { IsoCodeNames } from "@/constants/iso_code_names";
import type { TSubscriptionTariffResource } from "@/types/api/TSubscriptionTariffResource";
import type { TUserAccountResource } from "@/types/api/TUserAccountResource";
import type { TCurrencyResource } from "@/types/api/TCurrencyResource";
import { useI18nWrapper } from "@/composable/useI18nWrapper";
import { useSubscriptionsTariffs } from "@/composable/useSubscriptionsTariffs";
import {
  type TSubscriptionsBuyPostReq,
  useAccountGet,
  useSubscriptionsInfo,
  useSubscriptionsList,
} from "@/composable";
import { useUserBonusAccountGet } from "@/composable/API/useUserBonusAccountGet";
import { getCurrencyById } from "@/helpers/store";
import { prepareAccountBalance } from "@/helpers";
import { getCurrencySymbolByIso } from "@/helpers/account";
import { sortAccountsByBalance } from "@/helpers/sortAccountsByBalance";
import { useUserExchangeRates } from "@/composable/User/ExchangeRates";
import { useSubscriptionsBuyPost } from "@/composable/API/useSubscriptionsBuyPost";
import { TOAST_TYPE, useCallToast } from "@/composable/useCallToast";
import { useSubscriptionsModalFlow } from "@/components/Subscriptions/useSubscriptionsModalFlow";
import AccountsSelectV2 from "@/components/AccountsSelectV2/AccountsSelectV2.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UITransition from "@/components/ui/UITransition.vue";
import Loader from "@/components/ui/Loader/Loader.vue";
import UI2Text from "@/components/ui2/UI2Text/UI2Text.vue";
import UI2MessageBlock from "@/components/ui2/UI2MessageBlock/UI2MessageBlock.vue";
import UI2Button from "@/components/ui2/UI2Button/UI2Button.vue";
import Calculation from "@/components/Calculation/Calculation.vue";
import CalculationValueRow from "@/components/CalculationValueRow/CalculationValueRow.vue";
import CalculationExchangeRow from "@/components/CalculationExchangeRow/CalculationExchangeRow.vue";
import CalculationDivider from "@/components/CalculationDivider/CalculationDivider.vue";
import CalculationTotalRow from "@/components/CalculationTotalRow/CalculationTotalRow.vue";
import UI2Title from "@/components/ui2/UI2Title/UI2Title.vue";
import { SubscriptionUpLimitStep } from "@/components/Subscriptions/SubscriptionsModalsFlowV2/types";
import UI2FormGroup from "@/components/ui2/UI2FormGroup/UI2FormGroup.vue";
import UI2Tabs, { UI2Tab } from "@/components/ui2/UI2Tabs";

type Props = {
  variant: "short" | "full";
  steps: SubscriptionUpLimitStep[];
};

const props = defineProps<Props>();
const emit = defineEmits<{ done: [] }>();
const { t, formatCurrency } = useI18nWrapper();
const router = useRouter();
const { getCurrencySymbolById } = useSubscriptionsModalFlow();

const currentStep = defineModel<SubscriptionUpLimitStep>({ required: true });
const selectedTariff = ref<TSubscriptionTariffResource | null>(null);
const activeAccount = ref<TUserAccountResource | null>(null);

const buyTariffFormState = reactive<TSubscriptionsBuyPostReq>({
  subscription_tariff_id: 0,
  user_account_id: 0,
});

const isBuying = ref<boolean>(false);
const buyTariffError = ref<string>("");

const { subscriptionsTariffs, isFetching: isFetchingTariffs } =
  useSubscriptionsTariffs();
const { data: ratesData } = useUserExchangeRates();
const rates = computed(() => {
  return ratesData.value;
});

const raiseLimitTariffs = computed<TSubscriptionTariffResource[]>(() => {
  return (
    subscriptionsTariffs.value?.filter(
      (t) => t.status_name === "ACTIVE" && t.type_name === "EXTENSION"
    ) ?? []
  );
});

watchEffect(() => {
  if (
    !raiseLimitTariffs.value.length ||
    buyTariffFormState.subscription_tariff_id
  ) {
    return;
  }

  const firstTariff = raiseLimitTariffs.value[0];
  selectedTariff.value = firstTariff;
  buyTariffFormState.subscription_tariff_id = firstTariff.id;
});

watch(selectedTariff, (newValue) => {
  if (!newValue) return;
  buyTariffFormState.subscription_tariff_id = newValue.id;
});

const formattedTariffAmount = (tariff: TSubscriptionTariffResource): string => {
  const tariffAmount = Number(tariff.amount_first ?? tariff.amount);

  return `${getCurrencySymbolById(tariff.currency_id)}${tariffAmount.toFixed(
    2
  )}`;
};

const { data: accountsData, isFetching: isFetchingAccounts } = useAccountGet();

const accounts = computed(() => {
  return accountsData.value?.data ?? [];
});

watchEffect(() => {
  if (!activeAccount.value || buyTariffFormState.user_account_id) return;

  buyTariffFormState.user_account_id = activeAccount.value.id;
});

const usdtAccount = computed<TUserAccountResource | null>(() => {
  return accounts.value?.find((account) => account.currency_id === 15) ?? null;
});

const preselectAccount = () => {
  let accountID: number = 0;

  // Preselect USDT account if there is more than 1$ on its balance
  // Or preselect account with maximum balance
  if (usdtAccount.value && Number(usdtAccount.value.balance) > 1) {
    accountID = usdtAccount.value.id;
  } else {
    accountID = sortAccountsByBalance(accounts.value, rates.value)[0].id;
  }

  activeAccount.value =
    accounts.value.find((account) => account.id === accountID) ??
    accounts.value[0];
};

watch([() => accounts.value.length, rates.value], () => {
  if (accounts.value.length && rates.value && !activeAccount.value) {
    preselectAccount();
  }
});

const accountCurrency = computed<TCurrencyResource | null>(() => {
  if (!activeAccount.value) return null;

  return getCurrencyById(activeAccount.value.currency_id) ?? null;
});

const accountIsoCode = computed<IsoCodeNames | "">(() => {
  return (accountCurrency.value?.iso_code as IsoCodeNames) ?? "";
});

const handleAccountUpdate = (value?: TUserAccountResource | null) => {
  if (!value) return;

  buyTariffFormState.user_account_id = value.id;
  activeAccount.value = value;
};

const isAccountBalanceEnough = computed<boolean>(() => {
  if (!activeAccount.value) return false;

  const accountBalance = Number(activeAccount.value?.balance);
  return Number(totalAmount.value) < accountBalance;
});

const tariffAmount = computed(() => {
  if (!selectedTariff.value) return 0;

  return Number(
    selectedTariff.value?.amount_first ?? selectedTariff.value?.amount
  );
});

const tariffAmountWithSymbol = computed(() => {
  if (!selectedTariff.value) return "";

  return `${getCurrencySymbolById(
    selectedTariff.value.currency_id
  )}${tariffAmount.value.toFixed(2)}`;
});

const tariffAmountTitle = computed(() => {
  if (!selectedTariff.value) return "";

  return `${selectedTariff.value.name} (${t(
    "pst-private.card-pluralization",
    selectedTariff.value.cards_limit
  )})`;
});

const { data: userBonusAccountData } = useUserBonusAccountGet();

const availableBonusBalanceForPurchase = computed<number>(() => {
  return Math.min(
    Number(userBonusAccountData.value?.data?.balance ?? 0),
    tariffAmount.value
  );
});

const totalAmount = computed(() => {
  let total = tariffAmount.value - availableBonusBalanceForPurchase.value;
  if (!total || total <= 0) return "";
  const isoCode = accountIsoCode.value;
  const actualRates = rates.value;

  if (isoCode !== "USD") {
    total = total / actualRates?.[isoCode]?.["USD"];
  }
  return prepareAccountBalance(total, isoCode, false, BigNumber.ROUND_UP);
});

const totalAmountWithSymbol = computed<string>(() => {
  return `${accountCurrency.value?.symbol}${totalAmount.value}`;
});

const canBuyTariff = computed<boolean>(() => {
  return Boolean(
    buyTariffFormState.subscription_tariff_id &&
      buyTariffFormState.user_account_id &&
      isAccountBalanceEnough.value
  );
});

const handleContinueBtnClick = () => {
  if (!canBuyTariff.value) return;

  currentStep.value = SubscriptionUpLimitStep.CONFIRM;
};

const { fetchSubscriptionsInfo } = useSubscriptionsInfo({
  immediate: false,
});
const { fetchSubscriptionsList } = useSubscriptionsList({ immediate: false });

const buyTariff = async () => {
  if (!canBuyTariff.value) return;

  isBuying.value = true;
  const { data } = await useSubscriptionsBuyPost(buyTariffFormState);
  isBuying.value = false;

  if (!data.value?.data) {
    buyTariffError.value = data.value?.error?.full_code
      ? t(`errors.${data.value.error.full_code}`)
      : t("tariff.upLimit.errorBuyTariff");

    useCallToast({
      title: buyTariffError.value,
      options: {
        type: TOAST_TYPE.ERROR,
      },
    });
    return;
  }

  currentStep.value = SubscriptionUpLimitStep.SUCCESS;
  fetchSubscriptionsInfo();
  fetchSubscriptionsList();
};

const handleIssueCardBtnClick = async () => {
  await router.push({ name: RouteName.CREATE_CARD });
  emit("done");
};

const handleLaterBtnClick = async () => {
  await router.push({ name: RouteName.DASHBOARD });
  emit("done");
};

const handleConfirmBtnClick = () => {
  buyTariff();
};

const handlePurchaseBtnClick = () => {
  buyTariff();
};

const handleDoneBtnClick = () => {
  emit("done");
};
</script>

<template>
  <Loader v-if="isFetchingTariffs || isFetchingAccounts" />
  <div
    v-else
    class="max-w-[31.25rem] w-full mx-auto">
    <UITransition class="flex flex-col gap-10 justify-center">
      <div
        v-if="
          currentStep === SubscriptionUpLimitStep.SELECTION ||
          currentStep === SubscriptionUpLimitStep.PURCHASE
        "
        class="flex flex-col gap-10">
        <UI2Title
          v-if="props.variant === 'full'"
          :title="t('cards-raise-limit.package-title')"
          :subtitle="t('cards-raise-limit.package-subtitle')" />

        <div>
          <UI2Text
            type="body-s"
            weight="semi-medium"
            class="mb-3">
            {{
              t(
                props.variant === "full"
                  ? "cards-raise-limit.extension"
                  : "cards-raise-limit.choose-card-package"
              )
            }}
          </UI2Text>
          <UI2Tabs
            v-if="raiseLimitTariffs?.length"
            v-model="selectedTariff"
            data-testid="tariff-extension-tabs-select"
            class="w-full mt-3 p-1.5 gap-2"
            is-vertical>
            <UI2Tab
              v-for="tariff in raiseLimitTariffs"
              :key="tariff.id"
              :value="tariff"
              data-testid="tariff-extension-tab"
              custom
              :by="(t) => t.slug">
              <div class="p-4">
                <div class="flex items-center justify-between gap-2">
                  <UI2Text data-testid="tariff-extension-name">
                    {{ tariff.name }}
                  </UI2Text>
                  <UI2Text>
                    {{ formattedTariffAmount(tariff) }}
                  </UI2Text>
                </div>
                <div class="mt-2">
                  <UI2Text
                    type="body-l"
                    weight="medium">
                    {{
                      $t("pst-private.card-pluralization", tariff.cards_limit)
                    }}
                  </UI2Text>
                </div>
              </div>
            </UI2Tab>
          </UI2Tabs>
        </div>

        <div>
          <UI2Text
            type="body-s"
            weight="semi-medium"
            class="mb-3">
            {{ $t("cards-raise-limit.payment-method") }}
          </UI2Text>

          <UI2FormGroup
            :helper-text="
              !isAccountBalanceEnough ? t('errors.notEnoughFunds') : null
            "
            :invalid="!isAccountBalanceEnough">
            <AccountsSelectV2
              :model-value="activeAccount"
              :accounts="accounts"
              :invalid="!isAccountBalanceEnough"
              @update:model-value="handleAccountUpdate" />
          </UI2FormGroup>

          <Calculation
            v-if="selectedTariff"
            class="mt-3">
            <div class="flex flex-col gap-4">
              <CalculationValueRow
                v-if="availableBonusBalanceForPurchase"
                class="mb-4">
                <template #title>
                  <UI2Text
                    type="body-s"
                    weight="medium"
                    class="text-fg-purple">
                    {{ $t("subscriptions.page.bonuses") }}
                  </UI2Text>
                </template>

                <UI2Text
                  type="body-s"
                  weight="medium"
                  class="text-fg-purple">
                  -{{ availableBonusBalanceForPurchase.toFixed(2) }} $
                </UI2Text>
              </CalculationValueRow>

              <CalculationValueRow :title="tariffAmountTitle">
                {{ tariffAmountWithSymbol }}
              </CalculationValueRow>

              <CalculationExchangeRow
                v-if="accountIsoCode && accountIsoCode !== 'USD'"
                :value="`${getCurrencySymbolByIso(
                  accountIsoCode
                )}1 = ${formatCurrency(rates[accountIsoCode]['USD'])}`" />

              <CalculationDivider />

              <CalculationTotalRow
                :title="$t('cards-raise-limit.total')"
                :value="totalAmountWithSymbol" />
            </div>
          </Calculation>
        </div>

        <UI2MessageBlock
          :title="$t('subscriptionsUpLimitModal.information')"
          :subtitle="$t('subscriptionsUpLimitModal.informationItem')" />

        <UI2Button
          v-if="variant === 'full'"
          size="l"
          :disabled="!canBuyTariff"
          data-testid="tariff-extension-continue-btn"
          @click="handleContinueBtnClick">
          {{ $t("Continue") }}
        </UI2Button>
        <UI2Button
          v-else
          :loading="isBuying"
          :disabled="!canBuyTariff"
          size="l"
          data-testid="subscription.up-limit-process-purchase-btn"
          @click="handlePurchaseBtnClick">
          {{ $t("cards-raise-limit.purchase") }}
        </UI2Button>
      </div>

      <div
        v-else-if="currentStep === SubscriptionUpLimitStep.CONFIRM"
        class="flex flex-col gap-10">
        <div>
          <UI2Text
            type="h5"
            weight="medium">
            {{ $t("cards-raise-limit.confirm") }}
          </UI2Text>

          <UI2Text
            type="body-m"
            class="mt-2 text-fg-base-secondary">
            {{ $t("cards-raise-limit.confirm-subtitle") }}
          </UI2Text>
        </div>

        <div>
          <UI2Text
            type="body-s"
            weight="semi-medium"
            class="mb-3">
            {{ $t("cards-raise-limit.payment-method") }}
          </UI2Text>

          <AccountsSelectV2
            :model-value="activeAccount"
            :accounts="accounts"
            disabled
            class="my-3" />
        </div>

        <div>
          <Calculation>
            <div class="flex flex-col gap-4">
              <CalculationValueRow
                v-if="availableBonusBalanceForPurchase"
                class="mb-4">
                <template #title>
                  <UI2Text
                    type="body-s"
                    weight="medium"
                    class="text-fg-purple">
                    {{ $t("subscriptions.page.bonuses") }}
                  </UI2Text>
                </template>

                <UI2Text
                  type="body-s"
                  weight="medium"
                  class="text-fg-purple">
                  -{{ availableBonusBalanceForPurchase.toFixed(2) }} $
                </UI2Text>
              </CalculationValueRow>

              <CalculationValueRow :title="tariffAmountTitle">
                {{ tariffAmountWithSymbol }}
              </CalculationValueRow>

              <CalculationExchangeRow
                v-if="accountIsoCode && accountIsoCode !== 'USD'"
                :value="`${getCurrencySymbolByIso(
                  accountIsoCode
                )}1 = ${formatCurrency(rates[accountIsoCode]['USD'])}`" />

              <CalculationDivider />

              <CalculationTotalRow
                :title="$t('cards-raise-limit.total')"
                :value="totalAmountWithSymbol" />
            </div>
          </Calculation>
        </div>

        <UI2Button
          size="l"
          :loading="isBuying"
          :disabled="!canBuyTariff"
          data-testid="tariff-extension-confirm-btn"
          @click="handleConfirmBtnClick">
          {{ $t("Confirm") }}
        </UI2Button>
      </div>

      <div
        v-else-if="currentStep === SubscriptionUpLimitStep.SUCCESS"
        class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-full max-w-[33.75rem] px-5">
        <UI2Title
          variant="success"
          :title="t('cards-raise-limit.success-title')"
          :subtitle="t('cards-raise-limit.success-subtitle')" />

        <div
          v-if="props.variant === 'full'"
          class="flex flex-col gap-2 w-full">
          <UI2Button
            size="l"
            data-testid="tariff-extension-issue-card-btn"
            @click="handleIssueCardBtnClick">
            {{ t("cards-raise-limit.issue-now") }}
            <template #rightIcon>
              <DynamicIcon
                class="size-full"
                name="arrow-right" />
            </template>
          </UI2Button>

          <UI2Button
            size="l"
            variant="secondary"
            data-testid="tariff-extension-later-btn"
            @click="handleLaterBtnClick">
            {{ t("cards-raise-limit.issue-later") }}
          </UI2Button>
        </div>
        <UI2Button
          v-else
          size="l"
          data-testid="tariff-extension-done-btn"
          @click="handleDoneBtnClick">
          {{ $t("cards-raise-limit.done") }}
        </UI2Button>
      </div>
    </UITransition>
  </div>
</template>
