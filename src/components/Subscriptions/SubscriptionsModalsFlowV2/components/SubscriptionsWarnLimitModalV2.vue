<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import ModalOverlay from "@/components/ui/Modal/ModalOverlay.vue";
import ButtonBase from "@/components/ui/Button/ButtonBase.vue";
import { useUserStore } from "@/stores/user";
import UITransition from "@/components/ui/UITransition.vue";

const emit = defineEmits<{ close: []; "raise-limit": [] }>();

const { isTeamMember } = useUserStore();
</script>

<template>
  <Teleport to="body">
    <UITransition>
      <ModalOverlay @close="emit('close')">
        <div
          :class="$style.root"
          data-testid="subscription.card-limit-modal">
          <div class="flex items-center justify-between">
            <h3 class="text-[20px] font-medium">
              {{ $t("Card limit reached") }}
            </h3>
            <DynamicIcon
              name="close"
              class="w-6 h-auto cursor-pointer"
              @click="emit('close')" />
          </div>
          <template v-if="isTeamMember">
            <div>
              <p>{{ $t("MemberWarnLimitText") }}</p>
            </div>
          </template>
          <template v-else>
            <div>
              <p>{{ $t("Increase the limit to issue more cards") }}</p>
            </div>
            <ButtonBase
              :title="$t('Raise card limits')"
              class="w-full rounded-[6px]"
              data-testid="subscription.raise-limit-button"
              @click="emit('raise-limit')" />
          </template>
        </div>
      </ModalOverlay>
    </UITransition>
  </Teleport>
</template>

<style module lang="scss">
.root {
  @apply bg-white rounded-[6px] p-4 flex flex-col gap-6 w-[426px] max-w-[426px] font-hauss;
}
</style>
