<script lang="ts" setup>
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { computed, onMounted, onUnmounted, ref, watch, watchEffect } from "vue";
import type { TSubscriptionTariffResource } from "@/types/api/TSubscriptionTariffResource";
import { useSubscriptionsInfo } from "@/composable/useSubscriptionsInfo";
import { prepareAccountBalance } from "@/helpers/account";
import { maxBy } from "lodash";
import { useI18n } from "vue-i18n";
// eslint-disable-next-line max-len
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import UIFullScreenModal from "@/components/ui/UIFullScreenModal/UIFullScreenModal.vue";
import Loader from "@/components/ui/Loader/Loader.vue";
import {
  getCurrencySymbolByIso,
  type TIsoCode,
  useAccountGet,
  usePromoCodeAttachPost,
  useSubscriptionsBuyPost,
  useUserExchangeRatesGet,
  useTracker,
  TrackerEvent,
  useModalStack,
  useSubscriptionsPresalePost,
} from "@/composable";
import AccountsAndCardsSelect from "@/components/AccountsAndCardsSelect/AccountsAndCardsSelect.vue";
import { getAccountCurrencyByCurrencyId } from "@/helpers/getAccountCurrencyByCurrencyId";
import { IsoCodeNames } from "@/constants/iso_code_names";
import type { TUserAccountResource } from "@/types/api/TUserAccountResource";
import PromoCodeInput from "@/components/PromoCodeInput/PromoCodeInput.vue";
import type { TPromoCodeResource } from "@/types/api/TPromoCodeResource";
import Calculation from "@/components/Calculation/Calculation.vue";
import CalculationValueRow from "@/components/CalculationValueRow/CalculationValueRow.vue";
import CalculationDivider from "@/components/CalculationDivider/CalculationDivider.vue";
import CalculationTotalRow from "@/components/CalculationTotalRow/CalculationTotalRow.vue";
import { isSubscriptionCodeResource } from "@/components/PromoCodeInput/usePromoCodeInput";
import CalculationExchangeRow from "@/components/CalculationExchangeRow/CalculationExchangeRow.vue";
import { useUserStore } from "@/stores/user";
import { storeToRefs } from "pinia";
import { useUserBonusAccountGet } from "@/composable/API/useUserBonusAccountGet";
import BigNumber from "bignumber.js";
import { LocalStorageKey } from "@/constants/local_storage_key";

const props = defineProps<{
  selectTariff?: TSubscriptionTariffResource;
}>();

const emit = defineEmits<{
  close: [];
  "success-buy-subscription": [];
}>();

const tracker = useTracker();
const { t } = useI18n();
const { data: userBonusAccountData } = useUserBonusAccountGet();
const { fetchSubscriptionsInfo } = useSubscriptionsInfo();
const { data: accounts, execute: fetchAccounts } = useAccountGet();
const { data: ratesData } = useUserExchangeRatesGet();
const { isSuspicious } = storeToRefs(useUserStore());
const { openModal } = useModalStack();

const selectedAccount = ref<TUserAccountResource>();

const isLoading = ref<boolean>(true);
const isLoadBuyTariff = ref(false);
const promoCode = ref<TPromoCodeResource | null>(null);

const rates = computed<any>(() => {
  return ratesData.value?.data ?? null;
});

const uiErrorMessageKey = ref<string>("");
const currentAccountId = ref<number | null>(null);
const intervalUpdateUserExchangeRates = ref();

const updateAccounts = async () => {
  await fetchAccounts();
  // Update selected account for update all computeds
  changeAccountHandler(currentAccountId.value);
};

const INTERVAL_FOR_UPDATING_ACCOUNT_BALANCES_SEC = 10;
onMounted(() => {
  intervalUpdateUserExchangeRates.value = setInterval(() => {
    updateAccounts();
  }, INTERVAL_FOR_UPDATING_ACCOUNT_BALANCES_SEC * 1000);
});

onUnmounted(() => {
  clearInterval(intervalUpdateUserExchangeRates.value);
});

const findAccountById = (id: number) => {
  return accounts.value?.data?.find((item) => item.id === id);
};

const openModalTopUpAccountBalance = () => {
  openModal("transactions", {
    type: "deposit",
    toDirection: "account",
    toId: Number(isoCodeUsdOrEur.value ? usdtAccountId : currentAccountId),
  });
};

const subtitle = computed(() => {
  if (!props.selectTariff) {
    return "";
  }

  return `${props.selectTariff.name} — ${t(
    "pst-private.card-pluralization",
    props.selectTariff.cards_limit
  )}`;
});

const discountPercent = computed(() => {
  if (!promoCode.value) return 0;
  if (isSubscriptionCodeResource(promoCode.value.fields)) {
    return Number(
      promoCode.value.fields.subscription_purchase_discount_percent
    );
  }
  return 0;
});

const availableBonusBalanceForPurchase = computed<number>(() => {
  return Math.min(
    Number(userBonusAccountData.value?.data.balance ?? 0),
    totalAmountValueWithDiscount.value
  );
});

// Total amount
// With Discount
const totalAmountValueWithDiscount = computed<number>(() => {
  if (!totalAmountWithoutDiscount.value) return 0;
  return totalAmountWithoutDiscount.value * (1 - discountPercent.value / 100);
});

// Without discount
const totalAmountWithoutDiscount = computed(() => {
  return Number(props.selectTariff?.amount_first ?? props.selectTariff?.amount);
});

// With Exchange for current payment method (Total)
const totalAmountValue = computed(() => {
  let total =
    totalAmountValueWithDiscount.value - availableBonusBalanceForPurchase.value;
  const isoCode = accountIsoCode.value as TIsoCode;

  if (!total || !isoCode) return 0;

  // If currency is USD
  // No need to check rates - return total immediately
  if (isoCode === "USD") return total;

  const actualRates = rates.value;
  if (!actualRates) return 0;

  total = total / actualRates[isoCode]["USD"];
  return total;
});

// Total with currency symbol
const totalAmountCurrency = computed(() => {
  const isoCode = accountIsoCode.value as TIsoCode;
  return `${prepareAccountBalance(
    totalAmountValue.value,
    accountIsoCode.value as TIsoCode,
    true,
    BigNumber.ROUND_UP
  )} ${getCurrencySymbolByIso(isoCode)}`;
});

// Check is enough money on balance for payment
const currentAccountWithBalance = computed<boolean>(() => {
  if (!selectedAccount.value) return false;
  return Number(selectedAccount.value.balance) >= totalAmountValue.value;
});

const changeAccountHandler = (v: any) => {
  uiErrorMessageKey.value = "";
  currentAccountId.value = v;
  selectedAccount.value = findAccountById(v);
};

const isoCodeUsdOrEur = computed(() => {
  if (!accountIsoCode.value) return false;
  return ["USD", "EUR"].includes(accountIsoCode.value);
});

const accountIsoCode = computed<TIsoCode | null>(() => {
  if (selectedAccount.value) {
    return getAccountCurrencyByCurrencyId(selectedAccount.value.currency_id)
      .isoCode;
  }
  return null;
});

const usdtAccountId = computed(() => {
  return accounts.value?.data?.find(
    (item) =>
      getAccountCurrencyByCurrencyId(item.currency_id).isoCode ===
      IsoCodeNames.USDT
  );
});

const isShowPromoCodeInput = computed(() => {
  return !(
    Number(props.selectTariff?.amount_first) <
    Number(props.selectTariff?.amount)
  );
});

const setPromoCode = (code: TPromoCodeResource | undefined) => {
  promoCode.value = code ?? null;
};

// Not enough money, but user can't top up account
// In that case display disabled Buy button
const isBuyBtnDisabled = computed(() => {
  return !currentAccountWithBalance.value && isSuspicious.value;
});

const logBuySubscriptionEvent = () => {
  tracker.logEvent(TrackerEvent.USER_BUY_SUBSCRIPTION, {});

  const isNewLandingExperiment = localStorage.getItem(
    LocalStorageKey.NEW_PST_LANDING_EXPERIMENT
  );
  if (isNewLandingExperiment) {
    tracker.logEvent(TrackerEvent.BUY_SUBSCRIPTION_NEWPST);
  }
};

const buySubscriptionHandler = async () => {
  if (isBuyBtnDisabled.value) return;

  uiErrorMessageKey.value = "";
  isLoadBuyTariff.value = true;

  if (promoCode.value) {
    const { data: attachResponse } = await usePromoCodeAttachPost(
      promoCode.value.code
    );

    if (!attachResponse.value?.success) {
      uiErrorMessageKey.value = "errors.subscription-purchase-error";
      return;
    }
  }

  const { data } = await useSubscriptionsBuyPost({
    user_account_id: currentAccountId.value!,
    subscription_tariff_id: props.selectTariff?.id!,
  });

  isLoadBuyTariff.value = false;

  if (!data.value?.data) {
    const errMsgKey = data.value?.error?.full_code
      ? `errors.${data.value.error.full_code}`
      : "errors.subscription-purchase-error";

    uiErrorMessageKey.value = errMsgKey;
    return;
  }

  fetchSubscriptionsInfo();
  emit("success-buy-subscription");
  logBuySubscriptionEvent();
};

const accountWithMaxUsd = computed<any>(() => {
  const mapAccounts = accounts.value?.data?.map((account) => {
    return {
      id: account.id,
      balance: Number(account.balance_default),
    };
  });
  return maxBy(mapAccounts, "balance");
});

watchEffect(async () => {
  if (!currentAccountId.value && accountWithMaxUsd.value && rates.value) {
    isLoading.value = true;
    changeAccountHandler(accountWithMaxUsd.value.id);
    isLoading.value = false;
  }
});

const close = () => {
  emit("close");
};

watch(
  currentAccountId,
  (val) => {
    if (!val) return;

    useSubscriptionsPresalePost({
      subscription_tariff_id: props.selectTariff?.id!,
      user_account_id: val,
    });
  },
  { once: true }
);
</script>

<template>
  <UIFullScreenModal
    :is-open="true"
    :subtitle="subtitle"
    :title="`${$t('btn.purchase')} Private`"
    @close="close">
    <template #rightIcon>&nbsp;</template>
    <template #content>
      <div
        v-if="accounts && rates"
        data-testid="subscription-buy-modal"
        class="w-full flex flex-col gap-10 max-w-[28.75rem] bg-white m-auto mt-12">
        <h2 class="text-xl lg:text-2xl font-medium">
          {{ $t("pst-private.modal.subscription.pricing") }}
        </h2>
        <AccountsAndCardsSelect
          data-testid="accounts-and-cards-select"
          :accounts="accounts.data"
          :error="
            isLoading || currentAccountWithBalance
              ? ''
              : $t('errors.not-enough-money')
          "
          :label="$t('label.paymentMethod')"
          :model-value="currentAccountId"
          :rates="rates"
          :tariffs="[]"
          @update:model-value="changeAccountHandler" />

        <div class="flex flex-col gap-2.5">
          <Calculation>
            <CalculationValueRow v-if="availableBonusBalanceForPurchase">
              <template #title>
                <p class="text-4 font-medium leading-5 text-fg-purple">
                  {{ $t("cards.summary.bonuses") }}
                </p>
              </template>
              <p class="text-4 font-medium leading-5 text-fg-purple">
                -{{ availableBonusBalanceForPurchase.toFixed(2) }} $
              </p>
            </CalculationValueRow>
            <CalculationValueRow :title="`Private ${props.selectTariff?.name}`">
              <template #subtitle>
                <div
                  v-if="discountPercent"
                  class="text-fg-green font-medium text-3.5 leading-4">
                  {{ $t("cards.summary.discount-text") }}
                  {{ discountPercent }}%
                </div>
              </template>
              <div
                v-if="discountPercent"
                class="line-through text-fg-secondary mr-2">
                {{ totalAmountWithoutDiscount.toFixed(2) }}&nbsp;$
              </div>
              <div>
                {{
                  prepareAccountBalance(
                    totalAmountValueWithDiscount,
                    "USD",
                    true,
                    BigNumber.ROUND_UP
                  )
                }}&nbsp;$
              </div>
            </CalculationValueRow>
            <CalculationExchangeRow
              v-if="accountIsoCode && accountIsoCode !== 'USD'"
              :value="`1 ${accountIsoCode} = ${prepareAccountBalance(
                parseFloat(`${rates[accountIsoCode]['USD']}`).toFixed(2),
                accountIsoCode,
                true
              )} $`" />
            <CalculationDivider />
            <CalculationTotalRow
              data-testid="total-amount-row"
              :title="$t('pst-private.modal.details.table.summary')"
              :value="totalAmountCurrency" />
          </Calculation>

          <PromoCodeInput
            v-if="isShowPromoCodeInput"
            mode="subscription"
            @set-promo-code="setPromoCode" />
        </div>

        <template v-if="!isLoading">
          <UIButton
            v-if="currentAccountWithBalance || isSuspicious"
            v-track:button="{
              page: '/app/subscriptions/promo',
              page_version: 0,
              name: `Buy`,
              version: 0,
            }"
            class="w-full text-4 py-3 rounded"
            color="black"
            size="m"
            type="button"
            data-testid="connect-account-btn"
            :disabled="isBuyBtnDisabled"
            @click="buySubscriptionHandler">
            {{ `${$t("Connect")} Private ${props.selectTariff?.name}` }}
          </UIButton>
          <UIButton
            v-else
            class="col-span-2 mb-6 min-w-full font-medium"
            color="orange-solid"
            size="m"
            type="button"
            data-testid="top-up-account-btn"
            @click="openModalTopUpAccountBalance">
            <template #left>
              <DynamicIcon name="plus" />
            </template>
            <span class="font-medium text-white text-4 leading-5">
              {{ $t("pst-private.top-up-balance") }}
            </span>
          </UIButton>
        </template>

        <p
          v-if="uiErrorMessageKey"
          class="text-4 text-red-500 text-center my-6">
          {{ $t(uiErrorMessageKey) }}
        </p>
      </div>
      <Loader v-else />
    </template>
  </UIFullScreenModal>
</template>
