<script lang="ts">
export type TProps = {
  cardId: number;
  index?: number;
};

export type TRenameParams = {
  id: number;
  description?: string;
};
export type TEmits = {
  close: [];
  rename: [card: TRenameParams];
};
</script>
<script lang="ts" setup>
import { useI18n } from "vue-i18n";
import { computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Skeletor } from "vue-skeletor";
import CardDetails from "@/components/CardDetails/CardDetails.vue";
import UISideModal from "@/components/ui/UISideModal/UISideModal.vue";
import CardPreview from "@/components/CardPreview/CardPreview.vue";
import { getFormattedDate, getUtcDate } from "@/helpers/time";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import { useCardGet } from "@/composable/API/useCardGet";
import CardAutoRefill from "@/components/CardAutoRefill/CardAutoRefill.vue";
import LastPaymentsList from "@/components/LastPaymentsList/LastPaymentsList.vue";
import UIDropdownMenu from "@/components/ui/UIDropdownMenu/UIDropdownMenu.vue";
import type { TDropdownMenuItem } from "@/components/ui/UIDropdownMenu/TDropdownMenuItem";
import { useTransactionsV2Get } from "@/composable/API/useTransactionsV2Get";
import { RouteName } from "@/constants/route_name";
import { useUserStore } from "@/stores/user";
import UiTransition from "@/components/ui/UITransition.vue";
import { useCardIdPatch } from "@/composable/API/useCardIdPatch";
import { TOAST_TYPE, useCallToast } from "@/composable/useCallToast";
import { CARDS_STATUS } from "@/constants/cards_status";
import {
  useBusinessMembersTransactionsV2Get,
  useCardParams,
  useEventHooks,
  useModalStack,
} from "@/composable";
import { useCardsTableState } from "@/components/CardsTable/useCardsTableState";
import { isCardDisabledForTopUp } from "@/helpers/isCardDisabledForTopUp";
import { isCardBinDeprecated } from "@/helpers/isCardBinDeprecated";
import { useCountrySets } from "@/composable/useCountrySets";
import CardModalMessage from "@/components/CardModalFullInfo/CardModalMessage.vue";
import type { TTransferModalProps } from "@/components/TransferModal/types";
import { useCheckCardType } from "@/composable/useCheckCardType";

const props = defineProps<TProps>();

const emit = defineEmits<TEmits>();

const router = useRouter();
const route = useRoute();
const { t } = useI18n();
const userStore = useUserStore();
const { countrySetGuard } = useCountrySets();
const { isUltima } = useCheckCardType();
const { openModal, closeAllModals } = useModalStack();
const { changeCardBalanceHook } = useEventHooks();

const triggerRefetchCards = () => changeCardBalanceHook.trigger();

const { isTeamTable } = useCardsTableState();

const {
  data: cardData,
  isFetching: cardFetching,
  execute: updateCard,
} = useCardGet(props.cardId, {}, 0);

const cardDetail = computed(() => cardData.value?.data);

const {
  isCardActive,
  isBalanceNegative,
  isTopUpDisabled,
  isWithdrawDisabled,
  cardMinimumBalanceForBlock,
} = useCardParams(cardDetail);

const closeModal = () => {
  emit("close");
};

const reqTransactionConfig = {
  per_page: 3,
  sort: "processed_at",
  cards: String(props.cardId),
  direction: "desc",
};

const {
  data: paymentsData,
  isFetching: paymentsFetching,
  execute: updatePayments,
} = isTeamTable.value
  ? useBusinessMembersTransactionsV2Get(reqTransactionConfig)
  : useTransactionsV2Get(reqTransactionConfig);

const payments = computed(() => {
  return paymentsData.value?.data;
});

const openAllPayments = () => {
  router.push({
    name: isTeamTable.value
      ? RouteName.TEAM_PAYMENTS
      : RouteName.PERSONAL_PAYMENTS,
    query: { cards: props.cardId },
  });
  emit("close");
};

const openCardOperations = () => {
  router.push({
    name: isTeamTable.value
      ? RouteName.TEAM_OPERATIONS_BY_CARD
      : RouteName.OPERATIONS_BY_CARD,
    params: {
      cardId: props.cardId,
    },
  });
  emit("close");
};

const updateAutoRefill = () => {
  updateCard();
  updatePayments();
  triggerRefetchCards();
};

const setFavorite = async (state: boolean, id: number) => {
  const { error } = await useCardIdPatch(id, { favorite: !state });

  if (error.value) {
    return;
  }
  await updateCard();
  triggerRefetchCards();
};

const isMyCard = computed(() => {
  return (
    route?.name === RouteName.DASHBOARD ||
    route?.name === RouteName.CARDS ||
    userStore.user.email === cardDetail.value?.user_email
  );
});

const subtitle = computed(() => {
  if (!userStore.isTeamOwner) {
    return "";
  }
  if (isMyCard.value) {
    return "Master (You)";
  } else {
    return cardDetail.value?.user_email ?? "";
  }
});

const cardIsUnderMaintenance = computed(() => {
  return cardDetail.value?.under_maintenance;
});

const enableCardAutoRefill = computed(() => {
  return (
    isCardActive.value &&
    !cardIsBinDeprecated.value &&
    !isCardDisabledForTopUp(
      cardDetail.value?.mask,
      cardDetail.value!.created_at
    )
  );
});

const cardIsBinDeprecated = computed(() => {
  if (!cardDetail.value) return false;
  return isCardBinDeprecated(cardDetail.value);
});

const blockCardModalHandle = async () => {
  if (isBalanceNegative.value) {
    useCallToast({
      title: t("cards.cardsModalBlockCard.toast.title"),
      body: t("cards.cardsModalBlockCard.toast.body.balanceText", [
        cardMinimumBalanceForBlock.value,
      ]),
      options: {
        id: "cards-modal-full" + String(props.cardId),
        type: TOAST_TYPE.ERROR,
        timeout: 5000,
      },
    });
  } else {
    await openModal("blockCard", { cardId: props.cardId });
  }
};

const dropdownMenu = computed(() => {
  const arr: TDropdownMenuItem[] = [];

  if (isTeamTable.value) {
    arr.push({
      title: t("card.transferToMember"),
      callback: () => {
        if (cardIsBinDeprecated.value) {
          triggerRefetchCards();
          return;
        }
        closeAllModals();
        openModal("transferCardToMember", {
          cardId: props.cardId,
        });
      },
    });

    if (
      !(
        userStore.isTeamOwner &&
        userStore.user.email === cardDetail.value?.user_email
      )
    ) {
      arr.push({
        title: t("card.transferFromMember"),
        callback: () => {
          if (cardIsBinDeprecated.value) {
            triggerRefetchCards();
            return;
          }
          closeAllModals();
          openModal("transferCardFromMember", {
            card: cardDetail.value!,
          });
        },
      });
    }

    if (!isWithdrawDisabled.value) {
      arr.push({
        title: t("dashboardv2.myCards.withdraw"),
        callback: () => {
          withdraw();
        },
      });
    }

    if (cardDetail.value?.status === CARDS_STATUS.Active) {
      arr.push({
        title: t("block"),
        color: "red",
        callback: () => {
          blockCardModalHandle();
        },
      });
    }
  } else {
    arr.push({
      title: t("rename"),
      callback: () => {
        openModal("cardRename", { cardId: props.cardId });
      },
    });

    if (!isWithdrawDisabled.value) {
      arr.push({
        title: t("dashboardv2.myCards.withdraw"),
        callback: () => {
          withdraw();
        },
      });
    }

    if (userStore.isTeamOwner) {
      arr.push({
        title: t("card.transferToMember"),
        callback: () => {
          if (cardIsBinDeprecated.value) {
            triggerRefetchCards();
            return;
          }
          closeAllModals();
          openModal("transferCardToMember", {
            cardId: props.cardId,
          });
        },
      });
    }
    //Favorite
    if (isMyCard.value) {
      arr.push({
        title: cardDetail.value?.favorite
          ? t("card.favorite.remove")
          : t("card.favorite.add"),
        callback: () => {
          setFavorite(Boolean(cardDetail.value?.favorite), props.cardId);
        },
      });
    }
    if (cardDetail.value?.status === CARDS_STATUS.Active) {
      arr.push({
        title: t("block"),
        color: "red",
        callback: () => {
          blockCardModalHandle();
        },
      });
    }
  }

  return arr;
});

const deposit = () => {
  countrySetGuard(
    async () => {
      if (cardIsBinDeprecated.value) {
        triggerRefetchCards();
        return;
      }

      const options = isTeamTable.value
        ? isMyCard.value
          ? transferOptionsByType.value.depositMaster
          : transferOptionsByType.value.depositMember
        : transferOptionsByType.value.deposit;

      const transferModalRes = await openModal("transferModal", { ...options });
      if (transferModalRes.action === "complete") {
        updateAutoRefill();
      }
    },
    closeModal,
    closeModal,
    !isUltima(cardDetail.value?.tariff_id)
  );
};

const withdraw = async () => {
  if (cardIsBinDeprecated.value) {
    triggerRefetchCards();
    return;
  }

  const options =
    isTeamTable.value && !isMyCard.value
      ? transferOptionsByType.value.withdrawMember
      : transferOptionsByType.value.withdraw;

  const transferModalRes = await openModal("transferModal", { ...options });
  if (transferModalRes.action === "complete") {
    updateAutoRefill();
  }
};

const openUpgradeCardModal = () => {
  openModal("upgradeCardModal", { cardId: props.cardId });
};

type TTransferModalType =
  | "deposit"
  | "depositMaster"
  | "withdraw"
  | "withdrawMember"
  | "depositMember";

const transferOptionsByType = computed<
  Record<TTransferModalType, TTransferModalProps>
>(() => {
  return {
    deposit: {
      strategy: "userAccountsAndCardsToUserCardStrategy",
      toAccountId: cardDetail.value?.account.id,
    },
    depositMaster: {
      strategy: "masterAccountsAndCardsToMasterCardStrategy",
      toAccountId: cardDetail.value?.account.id,
    },
    depositMember: {
      strategy: "masterAccountsAndCardsToMemberCardStrategy",
      toAccountId: cardDetail.value?.account.id,
      memberId: cardDetail.value?.user_id,
    },
    withdraw: {
      strategy: "userCardToUserAccountsAndCardsStrategy",
      fromAccountId: cardDetail.value?.account.id,
    },
    withdrawMember: {
      strategy: "memberCardToMasterAccountsAndCardsStrategy",
      fromAccountId: cardDetail.value?.account.id,
      memberId: cardDetail.value?.user_id,
    },
  };
});
</script>

<template>
  <UISideModal
    :is-open="true"
    :index="props.index"
    @close="$emit('close')">
    <template #title>
      <div class="flex flex-auto truncate flex-col py-0.5">
        <div class="text-6 text-fg-primary leading-7 font-medium">
          {{ $t("cards.card") }}
          <span v-if="!isMyCard && cardDetail?.user_name">
            {{ cardDetail?.user_name }}
          </span>
        </div>
        <div
          v-if="subtitle"
          class="flex flex-none mt-1 text-4.5 text-fg-primary leading-6">
          <p class="truncate">
            {{ subtitle }}
          </p>
        </div>
      </div>
    </template>
    <template #content>
      <UiTransition>
        <div v-if="cardFetching || paymentsFetching || !cardDetail">
          <Skeletor
            as="div"
            class="rounded"
            height="10rem"
            width="100%" />

          <div class="grid grid-cols-2 gap-2 mt-4">
            <Skeletor
              class="rounded"
              height="2.5rem"
              width="100%" />
            <Skeletor
              class="rounded"
              height="2.5rem"
              width="100%" />
          </div>

          <Skeletor
            class="rounded mt-5"
            height="6.5rem"
            width="100%" />

          <Skeletor
            class="rounded mt-5"
            height="6.5rem"
            width="100%" />

          <Skeletor
            class="rounded mt-5"
            height="20rem"
            width="100%" />
        </div>

        <div v-else>
          <div class="flex flex-col w-full gap-10">
            <!-- Card preview-->
            <div class="flex flex-col">
              <div
                class="flex flex-none flex-col max-w-[17.688rem] w-full m-auto">
                <div class="flex flex-none w-full">
                  <CardPreview
                    v-if="cardDetail"
                    :card="cardDetail" />
                </div>
              </div>
              <div
                v-if="isCardActive"
                class="flex flex-col text-fg-secondary text-3.5 leading-4 mt-2 text-center">
                <div>
                  {{ $t("cards.next-card-payment") }}
                  {{
                    getFormattedDate(getUtcDate(cardDetail?.ordered_until), {
                      withYear: true,
                    })
                  }}
                </div>
              </div>
              <div class="flex flex-row mt-7 gap-1">
                <UIButton
                  :disabled="isTopUpDisabled"
                  class="flex flex-auto"
                  color="black"
                  size="m"
                  @click="deposit">
                  <template #left>
                    <DynamicIcon name="add" />
                  </template>
                  {{ $t("cards.deposit") }}
                </UIButton>

                <UIButton
                  :disabled="isWithdrawDisabled"
                  class="flex flex-auto"
                  color="black"
                  size="m"
                  @click="withdraw">
                  <template #left>
                    <DynamicIcon name="send-outline" />
                  </template>
                  {{ $t("cards.send") }}
                </UIButton>

                <UIDropdownMenu
                  :items="dropdownMenu"
                  placement="bottom-end">
                  <template #trigger>
                    <UIButton
                      :icon-only="true"
                      class="flex flex-auto"
                      color="grey-solid"
                      size="m">
                      <DynamicIcon
                        class="text-fg-primary"
                        name="more-vertical" />
                    </UIButton>
                  </template>
                </UIDropdownMenu>
              </div>
            </div>

            <CardModalMessage
              v-if="cardData?.data?.card_message"
              :card="cardData.data"
              @upgrade="openUpgradeCardModal" />

            <!-- Upgrade card (deprecated) -->
            <!--<CardBinDeprecatedDeprecated-->
            <!--  v-if="cardIsBinDeprecated"-->
            <!--  :card="cardDetail"-->
            <!--  @open-upgrade-card-modal="$emit('openUpgradeCardModal')" />-->

            <!-- Technical maintenance  -->
            <div
              v-if="cardIsUnderMaintenance"
              class="rounded bg-bg-orange-light p-4">
              <h3 class="text-lg font-medium">
                {{ $t("common.technical-work.title") }}
              </h3>
              <p class="mt-3">
                {{ $t("common.technical-work.descriptions") }}
              </p>
            </div>

            <!-- Card details -->
            <CardDetails :card="cardDetail" />

            <!-- Auto refill -->
            <CardAutoRefill
              v-if="enableCardAutoRefill"
              :auto-refill="cardDetail?.auto_refill"
              :card-id="cardDetail?.id"
              @update-auto-refill="updateAutoRefill" />

            <!-- Last payments -->

            <LastPaymentsList
              :is-loading="Boolean(paymentsFetching)"
              :payments="payments!">
              <!-- Open All Payments -->
              <div class="flex flex-col flex-none p-4">
                <UIButton
                  class="w-full"
                  color="grey-solid"
                  size="s"
                  @click="openAllPayments">
                  {{ $t("cards.open-all-payments") }}
                </UIButton>

                <UIButton
                  class="mt-2"
                  color="white"
                  size="s"
                  @click="openCardOperations">
                  {{ $t("operations.open-btn") }}
                </UIButton>
              </div>
            </LastPaymentsList>
          </div>
        </div>
      </UiTransition>
    </template>
  </UISideModal>
</template>

<style lang="scss" scoped>
.dropdown-list {
  @apply p-1.5 bg-bg-level-0 rounded border border-bg-level-2 min-w-[12.5rem];
  box-shadow: 0 10px 16px -3px rgba(20, 21, 26, 0.08),
    0 3px 6px -2px rgba(20, 21, 26, 0.05);

  &__item {
    @apply py-1 px-2 h-8 text-fg-primary text-3.5 leading-6 font-normal hover:bg-bg-level-1
    rounded transition-all cursor-pointer;
  }
}
</style>
