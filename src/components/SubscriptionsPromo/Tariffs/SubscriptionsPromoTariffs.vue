<script setup lang="ts">
// Core
import { computed, ref } from "vue";

// Composables & helpers & constants
import {
  TariffPeriod,
  useSubscriptionsInfoGet,
  useSubscriptionsList,
} from "@/composable";
import { useUserSubscriptionsTariffs } from "@/composable/useUserSubscriptionsTariffs";
import { SubscriptionStatusCode } from "@/constants/subscription_status_code";

// Components
import SubscriptionsPromoSectionHead from "@/components/SubscriptionsPromo/SubscriptionsPromoSectionHead.vue";
import SubscriptionsPromoTariffCards from "@/components/SubscriptionsPromo/Tariffs/SubscriptionsPromoTariffCards.vue";
import UI2Tabs, { UI2Tab, UI2TabLabel } from "@/components/ui2/UI2Tabs";
import UI2Badge from "@/components/ui2/UI2Badge/UI2Badge.vue";

const { data: subscriptionsInfo, isFetching: isFetchingSubscriptionInfo } =
  useSubscriptionsInfoGet();

const { currentMainTariff, isFetching: isFetchingCurrentMainTariff } =
  useSubscriptionsList();

const {
  subscriptionsTariffs,
  tariffsByPeriod,
  userSubscriptionsTariffs,
  isActiveNewSubscriptionsExperiment1,
  isActiveNewSubscriptionsExperimentTwo,
  isActiveSubscriptionsExperimentRetentionUsers,
  isFetching: isFetchingTariffs,
} = useUserSubscriptionsTariffs();

const isHaveSubscription = computed(() => {
  return (
    subscriptionsInfo.value?.data?.status === SubscriptionStatusCode.ACTIVE
  );
});

const isLoadingData = computed(() => {
  return (
    isFetchingTariffs.value ||
    isFetchingCurrentMainTariff.value ||
    isFetchingSubscriptionInfo.value
  );
});

const currentTariffPeriodTab = ref<TariffPeriod>(TariffPeriod.MONTHLY);
</script>

<template>
  <div class="tariffs">
    <SubscriptionsPromoSectionHead
      class="mb-10 items-center text-center"
      :title="$t('subscription-promo.monthly-plans')"
      :subtitle="$t('subscription-promo.plans-description')"
      :tag-text="$t('subscription-promo.pricing-n-terms')" />
    <UI2Tabs
      v-model="currentTariffPeriodTab"
      class="w-fit mx-auto mb-10">
      <UI2Tab :value="TariffPeriod.MONTHLY">
        <UI2TabLabel>{{ $t("pst-private.tabs.monthly") }}</UI2TabLabel>
      </UI2Tab>
      <UI2Tab :value="TariffPeriod.QUARTERLY">
        <UI2TabLabel>{{ $t("pst-private.tabs.quarterly") }}</UI2TabLabel>
        <UI2Badge color="purple">-20%</UI2Badge>
      </UI2Tab>
      <UI2Tab :value="TariffPeriod.SEMIANNUAL">
        <UI2TabLabel>{{ $t("pst-private.tabs.semiannual") }}</UI2TabLabel>
        <UI2Badge color="purple">-35%</UI2Badge>
      </UI2Tab>
    </UI2Tabs>

    <SubscriptionsPromoTariffCards
      :current-main-tariff="currentMainTariff"
      :is-active-experiment1="isActiveNewSubscriptionsExperiment1"
      :is-active-experiment2="isActiveNewSubscriptionsExperimentTwo"
      :is-active-experiment-retention="
        isActiveSubscriptionsExperimentRetentionUsers
      "
      :is-have-subscription="isHaveSubscription"
      :is-loading="isLoadingData"
      :active-tab="currentTariffPeriodTab"
      :tariffs="tariffsByPeriod[currentTariffPeriodTab] || []" />
    <div class="text-center py-2.5 text-fg-secondary">
      {{ $t("subscriptions.page.allCardsExcept") }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.tariffs {
  @apply relative;
}
</style>
