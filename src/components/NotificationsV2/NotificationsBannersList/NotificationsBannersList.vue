<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UI2Button from "@/components/ui2/UI2Button/UI2Button.vue";
import { useI18nWrapper } from "@/composable/useI18nWrapper";
import { RouteName } from "@/constants/route_name";
import { openExtension } from "@/helpers/events";
// eslint-disable-next-line max-len
import NotificationsBannersListItem from "@/components/NotificationsV2/NotificationsBannersList/components/NotificationsBannersListItem.vue";
import { useGetMobileAppBanner } from "@/composable/useGetMobileAppBanner";
import GetAppBannerTooltip from "@/components/GetAppBannerTooltip.vue";
import {
  type TNotificationBanner,
  TBannerSlug,
} from "@/composable/useNotificationsBanners/types";

const props = defineProps<{
  whatsappLink: string;
  telegramLink: string;
  banners: TNotificationBanner[];
}>();
const emit = defineEmits<{
  "connect-to-web-push": [];
  clicked: [value: TBannerSlug];
}>();

const { t } = useI18nWrapper();
const { isMobile, appLink } = useGetMobileAppBanner();

const clicked = (bannerSlug: TBannerSlug) => {
  emit("clicked", bannerSlug);
};

const connectToWebPushHandler = () => {
  emit("connect-to-web-push");
  clicked(TBannerSlug.WEBPUSH);
};

const extensionClickHandler = () => {
  openExtension();
  clicked(TBannerSlug.EXTENSION);
};

const findBySlug = (slug: TBannerSlug) => {
  return props.banners.find((banner) => banner.type === slug);
};

const isRead = (slug: TBannerSlug) => {
  return findBySlug(slug)?.read ?? false;
};
</script>

<template>
  <div class="flex flex-col gap-3">
    <NotificationsBannersListItem
      v-if="!!findBySlug(TBannerSlug.FEEDBACK)"
      :title="t('notifications.modal.banners-list.feedback-reward.title')"
      :read="isRead(TBannerSlug.FEEDBACK)">
      <template #actions>
        <router-link
          class="w-full"
          :to="{ name: RouteName.FEEDBACK_BONUS }">
          <UI2Button
            class="w-full"
            @click="clicked(TBannerSlug.FEEDBACK)">
            {{ t("notifications.modal.banners-list.feedback-reward.btn") }}
          </UI2Button>
        </router-link>
      </template>
      <template #image>
        <img
          alt=""
          class="default-img"
          src="@/assets/img/notification-banners/reward.png" />
      </template>
    </NotificationsBannersListItem>

    <NotificationsBannersListItem
      v-if="findBySlug(TBannerSlug.APP)"
      :title="t('notifications.modal.banners-list.get-app.title')"
      :read="isRead(TBannerSlug.APP)">
      <template #actions>
        <div class="relative group w-full">
          <a
            v-if="isMobile"
            :href="appLink"
            target="_blank">
            <UI2Button
              class="w-full"
              @click="clicked(TBannerSlug.APP)">
              {{ t("notifications.modal.banners-list.get-app.btn") }}
            </UI2Button>
          </a>

          <div v-else>
            <UI2Button
              class="w-full"
              @click="clicked(TBannerSlug.APP)">
              {{ t("notifications.modal.banners-list.get-app.btn") }}
            </UI2Button>
            <div class="hidden group-hover:flex pointer-events-none">
              <GetAppBannerTooltip class="absolute top-10" />
            </div>
          </div>
        </div>
      </template>
      <template #image>
        <img
          class="get-app-img"
          alt=""
          src="@/assets/img/notification-banners/get-app.png" />
      </template>
    </NotificationsBannersListItem>

    <NotificationsBannersListItem
      v-if="findBySlug(TBannerSlug.BOTS)"
      class="overflow-hidden"
      :read="isRead(TBannerSlug.BOTS)"
      :title="t('notifications.modal.banners-list.secure.title')">
      <template #actions>
        <a
          class="flex-1"
          :href="props.telegramLink"
          target="_blank">
          <UI2Button
            color="telegram"
            class="w-full"
            @click="clicked(TBannerSlug.BOTS)">
            Telegram
          </UI2Button>
        </a>
        <a
          class="flex-1"
          :href="props.whatsappLink"
          target="_blank">
          <UI2Button
            color="whatsapp"
            class="w-full"
            @click="clicked(TBannerSlug.BOTS)">
            Whatsapp
          </UI2Button>
        </a>
      </template>
      <template #image>
        <img
          class="default-img"
          alt=""
          src="@/assets/img/notification-banners/secure.png" />
      </template>
    </NotificationsBannersListItem>

    <NotificationsBannersListItem
      v-if="findBySlug(TBannerSlug.SOCIALS)"
      :read="isRead(TBannerSlug.SOCIALS)"
      :title="t('notifications.modal.banners-list.stay-tuned.title')">
      <template #actions>
        <a
          class="flex-1"
          href="https://t.me/pstdotnet"
          target="_blank">
          <UI2Button
            color="telegram"
            class="w-full"
            @click="clicked(TBannerSlug.SOCIALS)">
            Telegram
          </UI2Button>
        </a>
        <a
          class="flex-1"
          href="https://x.com/pstdotnet"
          target="_blank"
          @click="clicked(TBannerSlug.SOCIALS)">
          <UI2Button class="w-full"> X.com </UI2Button>
        </a>
      </template>
      <template #image>
        <img
          class="default-img"
          alt=""
          src="@/assets/img/notification-banners/stay-tuned.png" />
      </template>
    </NotificationsBannersListItem>

    <NotificationsBannersListItem
      v-if="findBySlug(TBannerSlug.EXTENSION)"
      class="overflow-hidden"
      :read="isRead(TBannerSlug.EXTENSION)"
      :title="t('notifications.modal.banners-list.cardholder.title')">
      <template #actions>
        <UI2Button
          class="w-full"
          @click="extensionClickHandler">
          <template #leftIcon>
            <DynamicIcon name="chrome-simple" />
          </template>
          {{ t("Connect") }}
        </UI2Button>
      </template>
      <template #image>
        <img
          class="cardholder-img"
          alt=""
          src="@/assets/img/notification-banners/cardholder.png" />
      </template>
    </NotificationsBannersListItem>

    <NotificationsBannersListItem
      v-if="findBySlug(TBannerSlug.WEBPUSH)"
      class="overflow-hidden"
      :read="isRead(TBannerSlug.WEBPUSH)"
      :title="t('notifications.modal.banners-list.webpush.title')">
      <template #actions>
        <UI2Button
          class="w-full"
          @click="connectToWebPushHandler">
          <template #leftIcon>
            <DynamicIcon name="announcement-01" />
          </template>
          {{ t("Connect") }}
        </UI2Button>
      </template>
      <template #image>
        <img
          class="webpush-img"
          alt=""
          src="@/assets/img/notification-banners/webpush.png" />
      </template>
    </NotificationsBannersListItem>
  </div>
</template>

<style scoped lang="scss">
.get-app-img {
  @apply -right-1.5 sm:-right-2.5 -bottom-[4.875rem] sm:-bottom-[5.25rem] absolute w-[12.5rem] sm:w-[13.75rem];
  clip-path: inset(0 0 78px 0);

  @screen sm {
    clip-path: inset(0 0 84px 0);
  }
}

.default-img {
  @apply absolute w-[9.6875rem] sm:w-[13.25rem] sm:right-5 right-4;
}

.cardholder-img {
  @apply absolute w-[11.25rem] sm:w-[14rem] -right-2 sm:right-2 sm:-bottom-[5.1875rem] -bottom-[3.4375rem];
}

.webpush-img {
  @apply absolute w-[12.125rem] sm:w-[16.125rem] -right-6 sm:-right-8 top-6 sm:top-2;
}
</style>
