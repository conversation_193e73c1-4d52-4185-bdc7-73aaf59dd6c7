<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UI2Text from "@/components/ui2/UI2Text/UI2Text.vue";
import { isMobile } from "@/helpers";

type Props = {
  title: string;
  read?: boolean;
};

const props = withDefaults(defineProps<Props>(), {});
</script>

<template>
  <div
    class="rounded-lg min-h-[9.75rem] relative sm:p-5 p-4 bg-bg-overlay-custom1 flex gap-3 items-center">
    <div class="flex flex-col basis-1/2 gap-4">
      <UI2Text
        class="sm:whitespace-pre-wrap"
        :type="isMobile ? 'body-l' : 'h6'">
        {{ props.title }}
      </UI2Text>
      <div class="flex flex-col sm:flex-row gap-1.5">
        <slot name="actions" />
      </div>
    </div>
    <slot name="image" />
    <DynamicIcon
      v-if="!props.read"
      name="notification-unread"
      class="absolute top-0 right-0" />
  </div>
</template>
