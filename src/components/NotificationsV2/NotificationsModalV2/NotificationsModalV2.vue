<script lang="ts" setup>
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UISideModal from "@/components/ui/UISideModal/UISideModal.vue";
import UI2Button from "@/components/ui2/UI2Button/UI2Button.vue";
import { isMobile } from "@/helpers";
import { computed, onUnmounted, ref } from "vue";
import { useI18n } from "vue-i18n";
import { useRouter } from "vue-router";
import {
  NotificationTabValue,
  TNotificationModalEmits,
} from "@/components/NotificationsV2/NotificationsModalV2/types";
import UITransition from "@/components/ui/UITransition.vue";
import UI2Text from "@/components/ui2/UI2Text/UI2Text.vue";
import Loader from "@/components/ui/Loader/Loader.vue";
// eslint-disable-next-line max-len
import NotificationsBannersList from "@/components/NotificationsV2/NotificationsBannersList/NotificationsBannersList.vue";
import NotificationsCardsList from "@/components/NotificationsV2/NotificationsCardsList/NotificationsCardsList.vue";
import { useNotifications } from "@/composable/useNotifications";
import UI2Tabs, {
  UI2Tab,
  UI2TabLabel,
  UI2TabsMarker,
} from "@/components/ui2/UI2Tabs";
import UI2Badge from "@/components/ui2/UI2Badge/UI2Badge.vue";
// eslint-disable-next-line max-len
import NotificationsMoneyRequestActivePreview from "@/components/NotificationsV2/NotificationsMoneyRequestActive/NotificationsMoneyRequestActivePreview.vue";
import { useNotificationsBanners } from "@/composable/useNotificationsBanners/useNotificationsBanners";
import { RouteName } from "@/constants/route_name";
import type { TBannerSlug } from "@/composable/useNotificationsBanners/types";

const emit = defineEmits<TNotificationModalEmits>();

const router = useRouter();
const { t } = useI18n();
const {
  notifications,
  unreadNotifications,
  notificationsTotal,
  isNotificationsLoading,
  currentPage,
  activeMoneyRequests,
  isRequestsLoading,
  getNotifications,
  unreadNotificationsTotal,
  getMoneyRequests,
  readNotifications,
  resetNotificationsData,
  activeMoneyRequestsTotal,
} = useNotifications();

const {
  connectToWebPush,
  availableBanners,
  bannersTotal,
  whatsappLink,
  telegramLink,
  isBannersLoading,
  bannerClicked,
} = useNotificationsBanners();

const activeTab = ref<NotificationTabValue>(NotificationTabValue.UNREAD);

const isLoading = computed(
  () => isNotificationsLoading.value || isRequestsLoading.value
);

const tabStateMap = computed<
  Record<
    NotificationTabValue,
    {
      counter: number;
      isLoading: boolean;
    }
  >
>(() => {
  return {
    "news-offers": {
      counter: bannersTotal.value,
      isLoading: isBannersLoading.value,
    },
    all: {
      counter: notificationsTotal.value + activeMoneyRequestsTotal.value,
      isLoading: isLoading.value,
    },
    unread: {
      counter: unreadNotificationsTotal.value + activeMoneyRequestsTotal.value,
      isLoading: isLoading.value,
    },
  };
});

const isEmptyFallbackVisible = computed(() => {
  const tabState = tabStateMap.value[activeTab.value];

  return tabState.counter === 0 && !tabState.isLoading;
});

const currentNotifications = computed(() => {
  switch (activeTab.value) {
    case NotificationTabValue.UNREAD:
      return unreadNotifications.value;
    case NotificationTabValue.ALL:
      return notifications.value;
    default:
      return [];
  }
});

/** Temporary disabled pagination for unread notifications */
const handleLoadMore = () => {
  if (activeTab.value === NotificationTabValue.UNREAD) return;

  getNotifications();
};

const initNotificationsAndRequests = async () => {
  await Promise.all([getNotifications(), getMoneyRequests()]);

  if (unreadNotifications.value.length) {
    readNotifications();
  } else if (!activeMoneyRequests.value.length) {
    activeTab.value = NotificationTabValue.ALL;
  }
};

const restartNotificationsAndRequests = (): Promise<[void, void]> => {
  resetNotificationsData();

  return Promise.all([getNotifications(), getMoneyRequests()]);
};

const handleTabChange = async () => {
  await restartNotificationsAndRequests();

  if (activeTab.value !== NotificationTabValue.NEWS_OFFERS) {
    readNotifications();
  }
};

onUnmounted(() => {
  unreadNotificationsTotal.value = 0;
});

initNotificationsAndRequests();

const handleBannerClick = (banner: TBannerSlug) => {
  bannerClicked(banner);
};
</script>
<template>
  <UISideModal
    is-open
    :title="t('notifications.modal.title')"
    @close="emit('close')">
    <template #content>
      <div
        class="flex items-center sticky top-0 z-[2] -mt-5 py-5 bg-bg-surface-neutral justify-between">
        <UI2Tabs
          v-model="activeTab"
          class="min-w-[72%] w-full sm:w-auto"
          @update:model-value="handleTabChange">
          <UI2Tab :value="NotificationTabValue.UNREAD">
            <UI2TabLabel>
              {{ t("notifications.modal.tabs.unread") }}
            </UI2TabLabel>

            <UI2Badge color="gray">
              {{ tabStateMap[NotificationTabValue.UNREAD].counter }}
            </UI2Badge>
          </UI2Tab>

          <UI2Tab :value="NotificationTabValue.ALL">
            <UI2TabLabel>
              {{ t("notifications.modal.tabs.all") }}
            </UI2TabLabel>
            <UI2Badge color="gray">
              {{ tabStateMap[NotificationTabValue.ALL].counter }}
            </UI2Badge>
          </UI2Tab>

          <UI2Tab
            v-slot="{ isActive }"
            :value="NotificationTabValue.NEWS_OFFERS">
            <UI2TabLabel
              weight="semi-medium"
              :class="
                isActive
                  ? 'text-fg-base-inverted'
                  : 'text-fg-accent-purple-primary'
              ">
              {{ t("notifications.modal.tabs.news-offers") }}
            </UI2TabLabel>

            <UI2Badge :color="isActive ? 'white' : 'purple'">
              {{ tabStateMap[NotificationTabValue.NEWS_OFFERS].counter }}
            </UI2Badge>
          </UI2Tab>

          <template #marker="{ selectedValue }">
            <UI2TabsMarker
              :class="{
                'bg-bg-button-help-primary':
                  selectedValue === NotificationTabValue.NEWS_OFFERS,
              }" />
          </template>
        </UI2Tabs>
        <UI2Button
          v-if="!isMobile"
          variant="secondary"
          @click="router.push({ name: RouteName.NOTIFICATIONS_SETTINGS })">
          <template #leftIcon>
            <DynamicIcon name="settings" />
          </template>
        </UI2Button>
      </div>

      <template
        v-if="
          activeTab === NotificationTabValue.UNREAD ||
          activeTab === NotificationTabValue.ALL
        ">
        <UITransition>
          <Loader
            v-if="
              isRequestsLoading || (isNotificationsLoading && currentPage === 1)
            " />
          <div v-else>
            <div
              v-if="activeMoneyRequests.length"
              class="flex flex-col gap-2 mb-5">
              <NotificationsMoneyRequestActivePreview
                v-for="request in activeMoneyRequests"
                :key="request.id"
                :request="request"
                :created-at="String(request.created_at)"
                @close-request-details="restartNotificationsAndRequests" />
            </div>

            <NotificationsCardsList
              v-if="currentNotifications.length"
              :items="currentNotifications"
              :is-loading-more="isNotificationsLoading"
              @load-more="handleLoadMore" />
          </div>
        </UITransition>
      </template>

      <template v-else-if="activeTab === NotificationTabValue.NEWS_OFFERS">
        <Loader v-if="isBannersLoading" />
        <NotificationsBannersList
          v-else
          :banners="availableBanners"
          :whatsapp-link="whatsappLink"
          :telegram-link="telegramLink"
          @clicked="handleBannerClick"
          @connect-to-web-push="connectToWebPush" />
      </template>

      <UITransition>
        <div
          v-if="isEmptyFallbackVisible"
          class="empty-container">
          <DynamicIcon
            class="w-6 h-6"
            name="folder-x" />
          <UI2Text type="body-l">
            {{ t("notifications.modal.empty-list") }}
          </UI2Text>
        </div>
      </UITransition>
    </template>
    <!-- TODO: to refactor to a new design -->
    <!-- <template #footer>
      <WebPushFooter />
      <NotificationSocialBadge />
    </template> -->
  </UISideModal>
</template>

<style lang="scss" scoped>
.empty-container {
  @apply flex flex-col gap-3 text-fg-base-quaternary
  absolute top-1/2 -translate-y-1/2 left-1/2 -translate-x-1/2 items-center text-center;
}
</style>
