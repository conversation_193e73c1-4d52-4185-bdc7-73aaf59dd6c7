<script setup lang="ts">
import { computed, ref } from "vue";
import { useSubscriptionsModalFlow } from "@/components/Subscriptions/useSubscriptionsModalFlow";
import { useRouter } from "vue-router";
import { LARGE_TARIFFS } from "@/constants/large_tariffs";
import { RouteName } from "@/constants/route_name";
import { useCountrySets } from "@/composable";
import { useSubscriptionsList } from "@/composable/useSubscriptionsList";
import type { TSubscriptionResource } from "@/types/api/TSubscriptionResource";
import UI2Text from "@/components/ui2/UI2Text/UI2Text.vue";
import UI2Button from "@/components/ui2/UI2Button/UI2Button.vue";
import UI2PrivateBlock from "@/components/ui2/UI2PrivateBlock/UI2PrivateBlock.vue";
import nonLargeBgImage from "@/assets/img/private_upgrade_bg.png";
import largeBgImage from "@/assets/img/private_large_tariff_upgrade_bg.png";
import ManagementSideModal from "@/components/SubscriptionDetailV2/components/SubscriptionManagementSideModal.vue";
import ExtensionManagementDialog from "@/components/SubscriptionDetailV2/components/ExtensionManagementDialog.vue";
/* eslint-disable max-len */
import ManagementScreenModal from "@/components/SubscriptionDetailV2/components/SubscriptionManagementScreenModal/SubscriptionManagementScreenModal.vue";
import SubscriptionTariffInfoBlocks from "@/components/SubscriptionDetailV2/components/SubscriptionTariffInfoBlocks.vue";
/* eslint-enable max-len */

const router = useRouter();
const { setModalState } = useSubscriptionsModalFlow();
const { countrySetGuard } = useCountrySets();
const { subscriptionsList } = useSubscriptionsList();
const isManagementSideModalOpen = ref(false);
const isManagementScreenModalOpen = ref(false);
const isExtensionManagementDialogOpen = ref(false);
const selectedSubscription = ref<TSubscriptionResource | null>(null);

const currentMainTariff = computed(() => {
  return subscriptionsList.value?.find(
    (s) => s.subscription_tariff.type_name === "MAIN"
  );
});

const tariffSlug = computed(() => {
  return currentMainTariff.value?.subscription_tariff?.slug.toLowerCase() ?? "";
});

const isLargeTariff = computed(() => {
  return LARGE_TARIFFS.includes(tariffSlug?.value);
});

const openUpLimitModal = () => {
  countrySetGuard(
    () => {
      setModalState(true, "subscriptionUpLimitModalFullScreen");
    },
    () => {},
    () => {},
    true
  );
};

const redirectToTariffs = () => {
  router.push({ name: RouteName.SUBSCRIPTION_TARIFF });
};

const onImproveTariff = () => {
  if (isLargeTariff.value) {
    openUpLimitModal();
  } else {
    redirectToTariffs();
  }
};

const handleCancelSubscription = (subscription: TSubscriptionResource) => {
  selectedSubscription.value = subscription;

  if (subscription.subscription_tariff.type_name === "MAIN") {
    isManagementScreenModalOpen.value = true;
  } else {
    isExtensionManagementDialogOpen.value = true;
  }
};
</script>
<template>
  <div>
    <div class="flex items-center justify-between">
      <UI2Text
        type="body-l"
        weight="medium"
        tag="h2">
        {{ $t("pst-private.subscription.info.block.title") }}
      </UI2Text>
      <div class="flex items-center gap-2">
        <UI2Button
          v-if="isLargeTariff"
          class="md:min-w-36"
          variant="secondary"
          @click="redirectToTariffs">
          {{ $t("pst-private.change-plan") }}
        </UI2Button>
        <UI2Button
          class="md:min-w-36"
          variant="secondary"
          data-testid="manage-subscription-btn"
          @click="isManagementSideModalOpen = true">
          {{ $t("pst-private.manage") }}
        </UI2Button>
      </div>
    </div>

    <div class="grid gap-3 grid-cols-1 md:grid-cols-4 mt-3">
      <SubscriptionTariffInfoBlocks />

      <!-- Improve Tariff Card -->
      <UI2PrivateBlock
        v-if="!isLargeTariff"
        class="min-h-36"
        type="upgrade"
        data-testid="pst-private.upgrade-plan-panel"
        :title="$t('pst-private.more-cards-n-cashback')"
        :text="$t('pst-private.upgrade-plan-text')"
        :btn-label="$t('pst-private.upgrade-plan')"
        :bg-image-url="nonLargeBgImage"
        @btn-click="onImproveTariff" />
      <UI2PrivateBlock
        v-else
        class="min-h-36"
        type="upgrade"
        :title="$t('pst-private.need-more-cards')"
        :text="$t('pst-private.need-more-cards-text')"
        :btn-label="$t('Raise the limit')"
        :bg-image-url="largeBgImage"
        @btn-click="onImproveTariff" />
    </div>

    <ManagementSideModal
      v-if="isManagementSideModalOpen"
      @cancel-subscription="handleCancelSubscription"
      @improve-tariff="onImproveTariff"
      @close="isManagementSideModalOpen = false" />

    <ManagementScreenModal
      v-if="isManagementScreenModalOpen"
      @close="isManagementScreenModalOpen = false" />

    <ExtensionManagementDialog
      v-if="isExtensionManagementDialogOpen && selectedSubscription"
      :extension="selectedSubscription"
      @close="isExtensionManagementDialogOpen = false" />
  </div>
</template>
