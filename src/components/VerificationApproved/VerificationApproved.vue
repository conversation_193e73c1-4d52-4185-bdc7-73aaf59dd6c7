<script setup lang="ts">
import { computed } from "vue";
import router from "@/router";
import { useI18nWrapper } from "@/composable/useI18nWrapper";
import { RouteName } from "@/constants/route_name";
import { IsoCodeNames } from "@/constants/iso_code_names";
import { useVerificationState } from "@/composable";
import UI2Title from "@/components/ui2/UI2Title/UI2Title.vue";
import UI2Text from "@/components/ui2/UI2Text/UI2Text.vue";
import UI2Button from "@/components/ui2/UI2Button/UI2Button.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import Loader from "@/components/ui/Loader/Loader.vue";

const emit = defineEmits<{
  "increase-limit": [];
  close: [];
}>();

const { t } = useI18nWrapper();
const { formatCurrency, formatNumber } = useI18nWrapper();

const { isLoading, verificationActualData, isComplete } =
  useVerificationState();

const userVerificationData = computed(() => {
  return verificationActualData?.value?.data ?? null;
});

const depositLimit = computed(() => {
  if (isComplete.value) {
    return t("verification.approve.unlimited");
  }

  return formatCurrency(
    Number(userVerificationData.value?.remaining_deposit),
    IsoCodeNames.USD,
    { fractionDigits: { min: 0, max: 0 } }
  );
});

const handleIssueCard = () => {
  router.push({ name: RouteName.CREATE_CARD });
};
</script>

<template>
  <div
    v-if="isLoading"
    class="fixed inset-0 flex items-center justify-center">
    <Loader />
  </div>
  <div
    v-else
    class="verification-approved">
    <UI2Title
      variant="success"
      data-testid="verification-approved"
      :title="$t('verification.approve.title', [userVerificationData?.step])"
      :subtitle="
        $t('verification.approve.subtitle', [userVerificationData?.step])
      " />

    <!-- Limits -->
    <div>
      <UI2Text
        type="body-s"
        weight="semi-medium">
        {{ $t("verification.approve.current-limits") }}
      </UI2Text>

      <div class="flex gap-3 mt-3">
        <div class="limit-card">
          <UI2Text type="body-s">
            {{ $t("verification.approve.deposit-limit") }}
          </UI2Text>

          <UI2Text
            class="mt-3"
            type="h5">
            {{ depositLimit }}
          </UI2Text>

          <UI2Text
            v-if="!isComplete"
            class="text-fg-base-quaternary">
            {{ $t("verification.approve.deposit-limit-period") }}
          </UI2Text>

          <UI2Button
            v-if="!isComplete"
            class="w-full mt-4"
            variant="secondary"
            @click="emit('increase-limit')">
            {{ $t("verification.approve.increase-limit") }}
          </UI2Button>
        </div>

        <div class="limit-card">
          <UI2Text type="body-s">
            {{ $t("verification.approve.card-limit") }}
          </UI2Text>

          <UI2Text
            class="mt-3"
            type="h5">
            {{ formatNumber(userVerificationData?.remaining_cards ?? 0) }}
            {{
              $t(
                "subscription-promo.card-pluralization",
                userVerificationData?.remaining_cards ?? 0
              )
            }}
          </UI2Text>

          <UI2Text
            v-if="!isComplete"
            class="text-fg-base-quaternary">
            {{ $t("verification.approve.card-limit-active") }}
          </UI2Text>

          <UI2Button
            v-if="!isComplete"
            class="w-full mt-4"
            variant="secondary"
            @click="emit('increase-limit')">
            {{ $t("verification.approve.increase-limit") }}
          </UI2Button>
        </div>
      </div>
    </div>

    <!-- Buttons -->
    <div class="flex flex-col gap-2">
      <UI2Button
        class="w-full"
        size="l"
        @click="handleIssueCard">
        {{ $t("kyc.complete.issue") }}

        <template #rightIcon>
          <DynamicIcon
            name="arrow-right"
            class="size-full" />
        </template>
      </UI2Button>

      <UI2Button
        class="w-full"
        variant="secondary"
        size="l"
        data-testid="verification-approved-close-button"
        @click="emit('close')">
        {{ $t("Close") }}
      </UI2Button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.verification-approved {
  @apply flex flex-col gap-10 max-w-[31.25rem] mx-auto;
}

.limit-card {
  @apply grow p-5 rounded-lg bg-bg-overlay-custom1;
}
</style>
