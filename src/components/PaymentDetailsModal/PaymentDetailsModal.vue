<script setup lang="ts">
import CalculationValueRow from "@/components/CalculationValueRow/CalculationValueRow.vue";
import CalculationCardRow from "@/components/CalculationCardRow/CalculationCardRow.vue";
import CalculationDateRow from "@/components/CalculationDateRow/CalculationDateRow.vue";
import Calculation from "@/components/Calculation/Calculation.vue";
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import UITag from "@/components/ui/UITag/UITag.vue";
import CalculationDivider from "@/components/CalculationDivider/CalculationDivider.vue";
import UISideModal from "@/components/ui/UISideModal/UISideModal.vue";
import PaymentsCashback from "@/components/PaymentsTable/PaymentsCashback.vue";
import PaymentPenaltyPopper from "@/components/PaymentsTable/PaymentPenaltyPopper.vue";
import { computed, nextTick, ref } from "vue";
import type { TTransactionResource } from "@/types/api/TTransactionResource";
import type { TUITagColor } from "@/components/ui/UITag/types";
import { useTransactionIdGetLinkGet } from "@/composable/API/useTransactionIdGetLinkGet";
import { useClipboard, useClipboardItems } from "@vueuse/core";
import { useCallToast, useModalStack } from "@/composable";
import { usePaymentsTransactions } from "../PaymentsTable/usePaymentsTransactions";
import { useUserStore } from "@/stores/user";
import { storeToRefs } from "pinia";
import PaymentsDetailSubTransactionRow from "@/components/PaymentsTable/PaymentsDetailSubTransactionRow.vue";
import UI2Button from "@/components/ui2/UI2Button/UI2Button.vue";
import { useI18nWrapper } from "@/composable/useI18nWrapper";
import type {
  TPaymentDetailsModalProps,
  TPaymentDetailsModalEmits,
} from "@/components/PaymentDetailsModal/types";

const { t } = useI18nWrapper();
const { openModal } = useModalStack();
const props = defineProps<TPaymentDetailsModalProps>();

const emit = defineEmits<TPaymentDetailsModalEmits>();

const isShowModal = ref(!!props.transaction);

const { isTeamMember } = storeToRefs(useUserStore());
const { getPaymentStatus } = usePaymentsTransactions();

const reqGetShareLinkLoading = ref(false);

const getTransactionShareLink = async () => {
  if (reqGetShareLinkLoading.value) return;
  reqGetShareLinkLoading.value = true;

  const prom: PromiseLike<string> = new Promise<string>((resolve) => {
    useTransactionIdGetLinkGet(Number(props.transaction.id)).then((res) => {
      resolve(String(res.data?.value?.data?.url));
    });
  });

  try {
    const { copy } = useClipboardItems();
    await copy([
      new ClipboardItem({
        "text/plain": prom,
      }),
    ]);
  } catch (err) {
    const { copy } = useClipboard();
    const link = await prom;
    await copy(link);
  }
  useCallToast({ title: t("link-copied-clipboard") });

  reqGetShareLinkLoading.value = false;
};

const isRefundType = computed(() => props.transaction.type_enum === "Refund");

const isCancelledStatus = computed(() => {
  const canceledTypes = ["Authorization", "Reversal"];

  return (
    props.transaction.custom_status === "Canceled" &&
    canceledTypes.includes(props.transaction.type_enum)
  );
});

/**
 * @returns {string|null} Translated declined status reason text if transaction is declined, null otherwise
 * @example 'insufficient_balance' -> 'insufficient-balance' if key exists, 'Insufficient balance' otherwise
 */
const declinedStatusReason = computed<string | null>(() => {
  const isStatusDeclined = Boolean(
    props.transaction.status === 35 &&
      !props.transaction.deleted_at &&
      props.transaction.status_text
  );

  if (!isStatusDeclined) return null;

  const statusText = props.transaction.status_text;

  return t(
    `operations.modal.detail-list.${statusText.replaceAll("_", "-")}`,
    statusText
      .replaceAll("_", " ")
      .replace(statusText[0], statusText[0].toUpperCase())
  );
});

const isSuccessfulStatus = computed(() => {
  const successfulTypes = ["Settlement", "Confirmation", "Clearing"];

  return (
    props.transaction.custom_status === "Success" &&
    successfulTypes.includes(props.transaction.type_enum ?? "")
  );
});

const statusUITagColor = computed<TUITagColor>(() => {
  // Using "grey-dark" color as default
  if (!props.transaction) return "grey-dark";

  const colors: Record<TTransactionResource["custom_status"], TUITagColor> = {
    Success: "green",
    Declined: "red",
    Hold: "orange",
    Refund: "grey-dark",
    Canceled: "grey-dark",
  };
  const status = getPaymentStatus(props.transaction);

  return colors[status] ?? "grey-dark";
});

const isSameCurrency = computed(() => {
  return (
    props.transaction.currency_code_original === null &&
    props.transaction.amount_original === null
  );
});

const handleTopUpCardClick = () => {
  openModal("transferModal", {
    strategy: "userAccountsAndCardsToUserCardStrategy",
    toAccountId: props.transaction.user_account_id,
  });
};

const closeModal = async () => {
  isShowModal.value = false;
  await nextTick();
  emit("close");
};
</script>

<template>
  <UISideModal
    :title="t('payments.detail.title')"
    :is-open="isShowModal"
    @close="closeModal">
    <template #content>
      <div
        v-if="transaction"
        class="payments-transaction flex flex-col my-4">
        <div class="flex flex-col items-center">
          <h3 class="text-10 text-fg-primary font-semibold text-center">
            <template
              v-if="
                transaction.amount_original &&
                transaction.currency_code_original
              ">
              {{
                `${transaction.amount_original} ${transaction.currency_code_original}`
              }}
            </template>
            <template v-else>{{ `${transaction.amount_total} $` }}</template>
          </h3>
          <span
            v-if="!isSameCurrency"
            class="text-xl text-center leading-4"
            >{{ `${transaction.amount_total} $` }}</span
          >
          <PaymentsCashback
            v-if="!isTeamMember"
            class="mt-4"
            :cashback-amount="transaction.cashback_amount" />
        </div>

        <Calculation class="mt-10">
          <div class="flex items-stretch justify-between gap-2">
            <div class="overflow-hidden">
              <p class="truncate font-medium">{{ transaction.description }}</p>
              <span class="text-3.5 truncate">ID {{ transaction.id }}</span>
            </div>
            <div class="flex items-center">
              <UITag
                :text="getPaymentStatus(transaction)"
                :color="statusUITagColor"
                size="m" />
            </div>
          </div>
          <CalculationDateRow
            v-if="isRefundType"
            :text="t('payments.date-entrolled')"
            :date="transaction.created_at" />
        </Calculation>

        <Calculation
          v-if="
            !isTeamMember &&
            transaction.cashback_amount &&
            Number(transaction.cashback_amount) > 0
          "
          class="mt-2">
          <CalculationValueRow :title="t('payments.detail.cashback')">
            <div class="p-1 flex items-center gap-1 leading-none">
              <DynamicIcon
                name="crown"
                class="w-4 h-4" />
              <span>
                {{ Number(transaction.cashback_amount).toFixed(1) }}
                $
              </span>
            </div>
          </CalculationValueRow>
        </Calculation>

        <Calculation class="mt-2">
          <CalculationCardRow
            v-if="transaction.card_tariff.id"
            :card-description="transaction.card_description"
            :card-mask="transaction.card_mask"
            :card-tariff-id="transaction.card_tariff.id" />

          <template v-if="isSuccessfulStatus">
            <CalculationDateRow
              :text="t('payments.detail.date-operation')"
              :date="transaction.authorization_at" />
            <CalculationDateRow
              v-if="transaction.settled_at"
              :text="t('payments.detail.date-confirmation')"
              :date="transaction.settled_at" />
          </template>

          <template v-else-if="isCancelledStatus">
            <CalculationDateRow
              :text="t('payments.detail.date-operation')"
              :date="transaction.processed_at" />
            <CalculationDateRow
              :text="t('payments.detail.date-cancellation')"
              :date="transaction.deleted_at" />
          </template>

          <CalculationDateRow
            v-else
            :text="t('payments.detail.date-operation')"
            :date="transaction.processed_at" />

          <CalculationDivider />

          <CalculationValueRow
            v-if="transaction.subtransactions.length"
            :title="t('Amount')">
            {{ transaction.amount }} $
          </CalculationValueRow>

          <PaymentsDetailSubTransactionRow
            v-for="item in transaction.subtransactions"
            :key="item.id"
            :transaction="item" />

          <CalculationValueRow :title="t('payments.detail.total')">
            {{ transaction.amount_total }} $
          </CalculationValueRow>

          <CalculationValueRow
            v-if="declinedStatusReason"
            :title="t('operations.modal.detail-list.reason-text')">
            <span class="text-fg-accent-red-primary">
              {{ declinedStatusReason }}</span
            >
          </CalculationValueRow>

          <UI2Button
            size="l"
            color="critical"
            @click="handleTopUpCardClick">
            {{ t("operations.modal.detail-list.top-up-card") }}
          </UI2Button>
        </Calculation>

        <PaymentPenaltyPopper
          v-if="Number(transaction.penalty_amount) > 0"
          :penalty-type="transaction.penalty_type"
          :penalty-amount="transaction.penalty_amount" />
      </div>
    </template>

    <template #footer>
      <div class="p-4">
        <UIButton
          :is-loading="reqGetShareLinkLoading"
          class="w-full"
          color="grey-solid"
          size="m"
          @click="getTransactionShareLink">
          {{ t("share") }}
        </UIButton>
      </div>
    </template>
  </UISideModal>
</template>

<style scoped lang="scss"></style>
