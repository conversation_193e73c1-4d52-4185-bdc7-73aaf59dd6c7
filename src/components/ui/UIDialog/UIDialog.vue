<template>
  <UIModal
    :is-open="props.isOpen"
    :title="props.title"
    :with-close-icon="props.withCloseIcon"
    @close="close">
    <template
      v-if="props.isOpen"
      #content>
      <slot name="content">
        <div
          class="flex flex-auto text-4 leading-5 text-fg-primary pb-4 mb-2"
          data-testid="ui-dialog.content">
          {{ props.text }}
        </div>
      </slot>
      <div
        v-if="props.showBtnCancel || props.showBtnConfirm || props.showBtnDeny"
        class="flex flex-none flex-col gap-2">
        <div class="flex flex-none flex-row gap-2 w-full">
          <UIButton
            v-if="props.showBtnCancel"
            :is-loading="props.isCancelLoading"
            color="grey-solid"
            class="w-full"
            data-testid="ui-dialog.cancel-btn"
            @click="callbackCancel">
            {{ props.btnCancelText || $t("cards.modal-btn-cancel-default") }}
          </UIButton>
          <UIButton
            v-if="props.showBtnConfirm"
            :is-loading="props.isConfirmLoading"
            color="black"
            class="w-full"
            data-testid="ui-dialog.confirm-btn"
            @click="callbackConfirm">
            {{ props.btnConfirmText || $t("cards.modal-btn-confirm-default") }}
          </UIButton>
        </div>
        <div
          v-if="props.showBtnDeny"
          class="flex flex-none w-full">
          <UIButton
            :is-loading="props.isDenyLoading"
            color="white"
            class="w-full"
            data-testid="ui-dialog.deny-btn"
            @click="callbackDeny">
            {{ props.btnDenyText || $t("cards.modal-btn-deny-default") }}
          </UIButton>
        </div>
      </div>
    </template>
  </UIModal>
</template>

<script setup lang="ts">
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import UIModal from "@/components/ui/UIModal/UIModal.vue";
import { UIDialogProps } from "./types";

const props = withDefaults(defineProps<UIDialogProps>(), {
  isOpen: false,
  title: "",
  text: "",

  showBtnConfirm: true,
  btnConfirmText: "",
  callbackConfirm: () => {},
  isConfirmLoading: false,

  showBtnCancel: true,
  btnCancelText: "",
  callbackCancel: () => {},
  isCancelLoading: false,

  showBtnDeny: false,
  btnDenyText: "",
  callbackDeny: () => {},
  isDenyLoading: false,

  withCloseIcon: false,
});

const emit = defineEmits<{
  close: [];
}>();

const close = () => {
  emit("close");
};
</script>
