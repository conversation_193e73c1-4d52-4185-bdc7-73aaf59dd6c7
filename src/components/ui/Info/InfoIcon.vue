<script setup lang="ts">
import DynamicIcon from "@/components/icons/DynamicIcon.vue";
import UITooltip from "@/components/ui/UITooltip/UITooltip.vue";

interface Props {
  type?: "information" | "error" | "warning";
  text: string;
}
const props = defineProps<Props>();
</script>

<template>
  <UITooltip
    popper-class="info-icon-popper max-w-[12.5rem]"
    placement="bottom-end"
    :arrow-padding="12"
    :distance="8">
    <span class="cursor-pointer">
      <DynamicIcon
        :name="'alert-circle'"
        class="w-4 h-4 min-w-4" />
    </span>
    <template #popper> {{ props.text }} </template>
  </UITooltip>
</template>

<style>
.v-popper__popper.info-icon-popper .v-popper__inner {
  @apply p-2.5 text-fg-tertiary text-base leading-4;
}
</style>
