<template>
  <Teleport to="body">
    <UITransition name="ui-fade">
      <div
        v-if="props.isOpen"
        data-testid="ui-modal.root"
        class="flex items-center justify-center bg-black bg-opacity-50 fixed z-8 left-0 top-0 w-full h-full"
        @click.self="close">
        <div
          ref="modalContent"
          class="ui-modal-content">
          <div class="flex items-start justify-between mb-4">
            <slot name="title">
              <h2
                v-if="props.title"
                class="text-6 flex-grow font-medium leading-7"
                data-testid="ui-modal.title">
                {{ props.title }}
              </h2>
            </slot>

            <div
              v-if="props.withCloseIcon"
              class="shrink-0 ml-auto">
              <UIButton
                data-testid="ui-modal.close-button"
                size="s"
                color="grey-free"
                icon-only
                @click="close">
                <CloseIcon />
              </UIButton>
            </div>
          </div>

          <Simplebar class="max-h-[28.1rem]">
            <slot name="content"> </slot>
          </Simplebar>
        </div>
      </div>
    </UITransition>
  </Teleport>
</template>

<script setup lang="ts">
import CloseIcon from "@/assets/svg/icon/close.svg";
import UIButton from "../UIButton/UIButton.vue";
import { useScrollLock } from "@vueuse/core";
import UITransition from "../UITransition.vue";
import { onMounted, onUnmounted, watch } from "vue";
import Simplebar from "simplebar-vue";

defineSlots<{
  content: (props: any) => any;
  title: (props: any) => any;
}>();

const props = defineProps<{
  title?: string;
  isOpen: boolean;
  withCloseIcon?: boolean;
}>();

const emit = defineEmits<{
  close: [];
}>();

const isLocked = useScrollLock(window.document);

watch(
  () => props.isOpen,
  () => {
    isLocked.value = props.isOpen;
  }
);

const close = () => {
  isLocked.value = false;
  emit("close");
};

const keydownHandler = (e: KeyboardEvent) => {
  if (e?.key === "Escape") close();
};

onUnmounted(() => {
  isLocked.value = false;
  window.removeEventListener("keydown", keydownHandler);
});

onMounted(() => {
  window.addEventListener("keydown", keydownHandler);
});
</script>

<style scoped>
.ui-modal-content {
  @apply rounded overflow-hidden p-4 bg-bg-level-0
  w-[28.75rem] max-w-[28.75rem] border border-bg-level-1 relative mx-2 mb-4.5;
}
</style>
