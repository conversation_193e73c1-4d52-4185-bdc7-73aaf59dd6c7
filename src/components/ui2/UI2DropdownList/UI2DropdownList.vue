<script setup lang="ts">
import { ref, computed } from "vue";
import { useScrollLock } from "@vueuse/core";
import { PopperWrapper } from "floating-vue";
import type { UI2DropdownListProps } from "@/components/ui2/UI2DropdownList/types";
import { UI_DROPDOWN_ITEM_DATA_ATTR } from "@/components/ui2/UI2DropdownList/constants";

const props = defineProps<UI2DropdownListProps>();
const isLocked = useScrollLock(document, false);
const DEFAULT_THEME = "ui2-dropdown-list";

defineSlots<{
  default(props: { shown: boolean }): void;
  content(props: { hide: () => void; shown: boolean }): void;
}>();

const slotContent = ref<HTMLDivElement | null>(null);

const getSelectableNodes = (): HTMLElement[] => {
  if (props.ignoreKeyboardEvents) return [];

  return slotContent.value
    ? Array.from(
        slotContent.value.querySelectorAll(`[${UI_DROPDOWN_ITEM_DATA_ATTR}]`)
      )
    : [];
};

const firstSelectableNode = computed(() => {
  return getSelectableNodes().length ? getSelectableNodes()[0] : null;
});

let currentFocusedNode: HTMLElement | null = null;

const focusCurrentNode = () => {
  if (currentFocusedNode === document.activeElement) return;

  currentFocusedNode?.focus();
};

const getNextElement = (): HTMLElement | null => {
  const nodes = getSelectableNodes();
  const indexOfCurrent = currentFocusedNode
    ? nodes.indexOf(currentFocusedNode)
    : 0;

  return nodes[indexOfCurrent + 1];
};

const getPreviousElement = (): HTMLElement | null => {
  const nodes = getSelectableNodes();
  const indexOfCurrent = currentFocusedNode
    ? nodes.indexOf(currentFocusedNode)
    : 0;

  return nodes[indexOfCurrent - 1];
};

/** Keyboard Navigation */
const handleKeydown = (e: KeyboardEvent) => {
  if (props.ignoreKeyboardEvents) return;

  let command: Function | null = null;

  switch (e.key) {
    case "Tab":
      command = () => {
        if (currentFocusedNode) return;

        if (firstSelectableNode.value) {
          currentFocusedNode = firstSelectableNode.value;

          setTimeout(() => {
            focusCurrentNode();
          });
        }
      };
      break;
    case "ArrowDown":
      command = () => {
        const nextElement = getNextElement();
        if (!nextElement) return;

        currentFocusedNode = nextElement;

        setTimeout(() => {
          focusCurrentNode();
        });
      };
      break;
    case "ArrowUp":
      command = () => {
        const prevElement = getPreviousElement() ?? firstSelectableNode.value;
        if (!prevElement) return;

        currentFocusedNode = prevElement;

        setTimeout(() => {
          focusCurrentNode();
        });
      };
      break;
  }

  // prevent default only for described in switch statement cases
  if (command) {
    command();
    e.preventDefault();
  }
};

const handleShowPopper = () => {
  if (firstSelectableNode.value) {
    setTimeout(() => {
      slotContent.value?.focus();
    });
  }
};

const handleHidePopper = () => {
  currentFocusedNode = null;
};
</script>

<template>
  <PopperWrapper
    v-bind="props.popper"
    :overflow-padding="20"
    no-auto-focus
    auto-boundary-max-size
    :theme="props.popper?.theme ?? DEFAULT_THEME"
    @apply-show="handleShowPopper"
    @apply-hide="handleHidePopper">
    <template #default="slotProps">
      <slot v-bind="slotProps" />
    </template>

    <template #popper="slotProps">
      <div
        ref="slotContent"
        class="overflow-hidden p-1 outline-none flex flex-col"
        tabindex="0"
        @mouseenter="isLocked = true"
        @mouseleave="isLocked = false"
        @keydown="handleKeydown">
        <slot
          name="content"
          v-bind="slotProps" />
      </div>
    </template>
  </PopperWrapper>
</template>

<style lang="scss">
.v-popper__popper {
  @apply z-9 pointer-events-auto;

  .v-popper__arrow-outer,
  .v-popper__arrow-inner {
    visibility: hidden;
  }

  &.v-popper--theme-ui2-dropdown-list {
    @apply bg-bg-surface-neutral border border-border-action-default-normal rounded-lg;
    box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.07),
      0px 10px 16px -3px rgba(20, 21, 26, 0.08),
      0px 3px 6px -2px rgba(20, 21, 26, 0.05);
  }
}

.v-popper__inner > div {
  @apply flex flex-col overflow-hidden;
}
</style>
