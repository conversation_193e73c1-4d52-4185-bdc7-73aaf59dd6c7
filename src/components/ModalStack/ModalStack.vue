<script setup lang="ts">
import CardModalFullInfo from "@/components/CardModalFullInfo/CardModalFullInfo.vue";
import CardUpgradeAlertDialog from "@/components/CardUpgradeDialog/CardUpgradeAlertDialog.vue";
import CardUpgradeDialog from "@/components/CardUpgradeDialog/CardUpgradeDialog.vue";
import CardUpgradeSuccessDialog from "@/components/CardUpgradeDialog/CardUpgradeSuccessDialog.vue";
import CardsModalAutoRefill from "@/components/CardsModalAutoRefill.vue";
import CardsModalBlock from "@/components/CardsModalBlock.vue";
import CardsModalRename from "@/components/CardsModalRename.vue";
import CardTransferModal from "@/components/CardsTable/CardTransferModal.vue";
import GetMobileAppBanner from "@/components/GetMobileAppBanner/GetMobileAppBanner.vue";
import InvationModal from "@/components/Invation/InvationModal.vue";
import PersonalNotificationModal from "@/components/PersonalNotification/PersonalNotificationModal.vue";
import SupportSideModal from "@/components/SupportSideModal/SupportSideModal.vue";
import TransferFromMemberModal from "@/components/TransferFromMemberModal/TransferFromMemberModal.vue";
import TransferModal from "@/components/TransferModal/TransferModal.vue";
import UserAuthProviderModal from "@/components/UserAuthProviderModal/UserAuthProviderModal.vue";

import {
  useModalStack,
  useUserPersonalNotificationGet,
  useCardGet,
} from "@/composable";
import { useNotifications } from "@/stores/notifications";
import { storeToRefs } from "pinia";
import { useRoute } from "vue-router";
import NotificationsModalV2 from "@/components/NotificationsV2/NotificationsModalV2/NotificationsModalV2.vue";
// eslint-disable-next-line max-len
import NotificationDetailsModal from "@/components/NotificationsV2/NotificationsDetailsModal/NotificationDetailsModal.vue";
import WithdrawalOfFundsModal from "@/components/WithdrawalOfFundsModal/WithdrawalOfFundsModal.vue";
import WithdrawalToAccountForUserPstModal from "@/components/WithdrawalToAccountForUserPstModal.vue";
import WithdrawalToWalletExternalModal from "@/components/WithdrawalToWalletExternalModal.vue";
import { watch } from "vue";
import { useScrollLock } from "@vueuse/core";
import CardDetailsUnavailableModal from "@/components/CardDetails/CardDetailsUnavailableModal.vue";
import TotalRefundModal from "../TotalRefundModal.vue";
// eslint-disable-next-line max-len
import NotificationsMoneyRequestActiveDetails from "@/components/NotificationsV2/NotificationsMoneyRequestActive/NotificationsMoneyRequestActiveDetails.vue";
import TransactionsModal from "@/components/TransactionsModal/TransactionsModal.vue";
import { TTransactionsModalProps } from "@/components/TransactionsModal/types";
import PaymentDetailsModal from "@/components/PaymentDetailsModal/PaymentDetailsModal.vue";

const { modalStack, closeWithAction, openModal, closeAllModals } =
  useModalStack();
// const { openGetMobileBanner } = useGetMobileAppBanner();
const isDocumentLocked = useScrollLock(window.document);
const route = useRoute();

const handleEvent = (action: string, payload: any = {}) => {
  closeWithAction(action, payload);
};

const openCardDetailModal = () => {
  if (
    route?.query?.deprecated_card_id &&
    !Array.isArray(route.query.deprecated_card_id)
  ) {
    const cardId = Number(route.query.deprecated_card_id);
    openModal("cardDetailFull", { cardId });
  }
};

const openPersonalNotificationModal = async () => {
  const res = await useUserPersonalNotificationGet();
  if (res.data.value?.data) {
    openModal("personalNotification", {
      notification: res.data.value.data,
    });
  }
};

const openTeamInviteModal = async () => {
  const notificationsStore = useNotifications();
  const { teamInvites } = storeToRefs(notificationsStore);
  await notificationsStore.getAll();

  if (teamInvites.value[0]) {
    openModal("teamInvite", { invitation: teamInvites.value[0] });
  }
};

const openTransferModal = async () => {
  const action = route.query.action;
  const toId = route.query.to_id;

  if (!toId || action !== "transfer") {
    return;
  }

  const { data: cardData } = await useCardGet(Number(toId));

  if (!cardData.value?.data) return;

  const toAccountId = cardData.value.data.account.id;

  if (toAccountId) {
    openModal("transferModal", {
      strategy: "userAccountsAndCardsToUserCardStrategy",
      toAccountId,
    });
  }
};

const openTransactionsModal = async () => {
  const {
    action,
    type,
    to_direction: toDirection,
    to_id: toId,
    to_amount: toAmount,
    from_direction: fromDirection,
    from_id: fromId,
    from_amount: fromAmount,
    to_email: toEmail,
    to_address: toAddress,
  } = route.query;

  if (action !== "transaction") {
    return;
  }

  openModal("transactions", {
    type: type as TTransactionsModalProps["type"],
    toDirection: toDirection as TTransactionsModalProps["toDirection"],
    toId: toId ? Number(toId) : undefined,
    toAmount: toAmount ? Number(toAmount) : undefined,
    fromId: fromId ? Number(fromId) : undefined,
    fromDirection: fromDirection as TTransactionsModalProps["fromDirection"],
    fromAmount: fromAmount ? Number(fromAmount) : undefined,
    toEmail: toEmail as TTransactionsModalProps["toEmail"],
    toAddress: toAddress as TTransactionsModalProps["toAddress"],
  });
};

watch(
  () => modalStack.value.length,
  () => {
    isDocumentLocked.value = modalStack.value.length > 0;
  }
);

watch(
  () => route.name,
  () => {
    closeAllModals();
  }
);

openTeamInviteModal();
openCardDetailModal();
openPersonalNotificationModal();
openTransferModal();
openTransactionsModal();
</script>

<template>
  <div class="modal-stack">
    <template
      v-for="(modal, idx) in modalStack"
      :key="idx">
      <CardModalFullInfo
        v-if="modal.type === 'cardDetailFull' && modal.props.cardId"
        v-bind="modal.props"
        :index="idx"
        @rename="(payload) => handleEvent('rename', payload)"
        @close="closeWithAction('close')" />

      <CardsModalRename
        v-if="modal.type === 'cardRename' && modal.props.cardId"
        v-bind="modal.props"
        :index="idx"
        @close="closeWithAction('close')" />

      <CardsModalBlock
        v-if="modal.type === 'blockCard'"
        v-bind="modal.props"
        @close="closeWithAction('close')" />

      <CardTransferModal
        v-if="modal.type === 'transferCardToMember'"
        v-bind="modal.props"
        @close="closeWithAction('close')" />

      <TransferFromMemberModal
        v-if="modal.type === 'transferCardFromMember'"
        v-bind="modal.props"
        @close="closeWithAction('close')" />

      <CardsModalAutoRefill
        v-if="modal.type === 'cardAutoRefill'"
        v-bind="modal.props"
        @close="closeWithAction('close')" />

      <CardUpgradeDialog
        v-if="modal.type === 'upgradeCardModal'"
        v-bind="modal.props"
        @success="(payload) => handleEvent('success', payload)"
        @close="closeWithAction('close')" />

      <CardUpgradeSuccessDialog
        v-if="modal.type === 'upgradeCardSuccess'"
        v-bind="modal.props"
        @close="closeWithAction('close')" />

      <CardUpgradeAlertDialog
        v-if="modal.type === 'binDeprecatedAttention'"
        v-bind="modal.props"
        @close="closeWithAction('close')" />

      <TransferModal
        v-if="modal.type === 'transferModal'"
        v-bind="modal.props"
        :index="idx"
        @complete="() => handleEvent('complete')"
        @close="closeWithAction('close')" />

      <UserAuthProviderModal
        v-if="modal.type === 'user2FAProvider'"
        v-bind="modal.props"
        @close="closeWithAction('close')" />

      <GetMobileAppBanner
        v-if="modal.type === 'getMobileAppBanner'"
        @close="closeWithAction('close')" />

      <PersonalNotificationModal
        v-if="modal.type === 'personalNotification'"
        :notification="modal.props.notification"
        @close="closeWithAction('close')" />

      <InvationModal
        v-if="modal.type === 'teamInvite'"
        :invitation="modal.props.invitation"
        @close="closeWithAction('close')" />

      <SupportSideModal
        v-if="modal.type === 'supportSideModal'"
        :index="idx"
        v-bind="modal.props"
        @close="closeWithAction('close')" />

      <NotificationsModalV2
        v-if="modal.type === 'systemNotification'"
        :index="idx"
        @close="closeWithAction('close')" />

      <NotificationDetailsModal
        v-if="modal.type === 'notificationDetailModal'"
        v-bind="modal.props"
        :index="idx"
        @close-all="closeAllModals"
        @close="closeWithAction('close')" />

      <WithdrawalOfFundsModal
        v-if="modal.type === 'withdrawalOfFunds'"
        v-bind="modal.props"
        :index="idx"
        @close="closeWithAction('close')" />

      <WithdrawalToAccountForUserPstModal
        v-if="modal.type === 'withdrawalToAccountForPstUser'"
        v-bind="modal.props"
        :index="idx"
        @close="closeWithAction('close')" />

      <WithdrawalToWalletExternalModal
        v-if="modal.type === 'withdrawalToWalletExternal'"
        v-bind="modal.props"
        :index="idx"
        @close="closeWithAction('close')" />

      <CardDetailsUnavailableModal
        v-if="modal.type === 'cardDetailsUnavailable'"
        v-bind="modal.props"
        :index="idx"
        @close="closeWithAction('close')" />

      <TotalRefundModal
        v-if="modal.type === 'totalRefund'"
        v-bind="modal.props"
        :index="idx"
        @close="closeWithAction('close')" />

      <NotificationsMoneyRequestActiveDetails
        v-if="modal.type === 'notificationMoneyRequestDetails'"
        v-bind="modal.props"
        :index="idx"
        @close="closeWithAction('close')" />

      <TransactionsModal
        v-if="modal.type === 'transactions'"
        v-bind="modal.props"
        :index="idx"
        @close="closeWithAction('close')" />

      <PaymentDetailsModal
        v-if="modal.type === 'paymentDetails'"
        v-bind="modal.props"
        :index="idx"
        @close="closeWithAction('close')" />
    </template>
  </div>
</template>
