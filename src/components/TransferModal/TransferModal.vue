<script setup lang="ts">
import UISideModal from "@/components/ui/UISideModal/UISideModal.vue";
import TransferFormContainer from "./TransferFormContainer.vue";
import DynamicIcon from "../icons/DynamicIcon.vue";
import UIButton from "@/components/ui/UIButton/UIButton.vue";
import { ref } from "vue";
import { useEventHooks } from "@/composable/useEventHooks";
import type {
  FormStage,
  TTransferModalEmits,
  TTransferModalProps,
} from "@/components/TransferModal/types";
import { omit } from "lodash";
import { useRoute, useRouter } from "vue-router";
import { TRANSACTION_QUERY_KEYS } from "@/components/TransactionsModal/constants";

const props = withDefaults(defineProps<TTransferModalProps>(), {
  index: 0,
});
const emit = defineEmits<TTransferModalEmits>();

const router = useRouter();
const route = useRoute();
const { changeAccountHook, changeCardBalanceHook } = useEventHooks();

const currentStage = ref<FormStage>("calculate");

const onUpdateCurrentStage = (stage: FormStage) => {
  currentStage.value = stage;
};

const closeModal = () => {
  router.replace({ query: omit(route.query, TRANSACTION_QUERY_KEYS) });

  if (currentStage.value === "transfer-completed") {
    triggerRefetch();
    emit("complete");
  } else {
    emit("close");
  }

  currentStage.value = "calculate";
};

const triggerRefetch = () => {
  changeAccountHook.trigger();
  changeCardBalanceHook.trigger();
};
</script>
<template>
  <UISideModal
    :can-go-back="props.index > 0"
    is-open
    :index="props.index"
    @back="closeModal"
    @close="closeModal">
    <template #title>
      <div class="flex flex-row flex-auto items-center">
        <div class="flex flex-row">
          <div
            v-if="currentStage === 'send'"
            class="flex flex-none mr-3">
            <UIButton
              icon-only
              color="grey-free"
              size="s"
              @click="currentStage = 'calculate'">
              <DynamicIcon
                name="arrow-left-thin"
                class="w-6 h-6 text-fg-secondary" />
            </UIButton>
          </div>
        </div>
        <div
          class="flex flex-none text-fg-primary text-6 leading-7 font-medium">
          {{ $t("transfer-modal.modal-title") }}
        </div>
      </div>
    </template>
    <template #content="{ contentHeight }">
      <div :style="{ height: contentHeight - 35 + 'px' }">
        <TransferFormContainer
          :current-stage="currentStage"
          :from-account-id="props.fromAccountId"
          :to-account-id="props.toAccountId"
          :member-id="props.memberId"
          :strategy="props.strategy"
          :amount="props.amount"
          @update-stage="onUpdateCurrentStage"
          @close="closeModal" />
      </div>
    </template>
  </UISideModal>
</template>
