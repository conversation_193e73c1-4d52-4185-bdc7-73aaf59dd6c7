import type {
  TEmits as TCardDetailFullEmits,
  TProps as TCardDetailFullProps,
} from "@/components/CardModalFullInfo/CardModalFullInfo.vue";
import type {
  TEmits as TCardAutoRefillEmits,
  TProps as TCardAutoRefillProps,
} from "@/components/CardsModalAutoRefill.vue";
import type {
  TEmits as TBlockCardEmits,
  TProps as TBlockCardProps,
} from "@/components/CardsModalBlock.vue";
import type {
  TEmits as TCardRenameEmits,
  TProps as TCardRenameProps,
} from "@/components/CardsModalRename.vue";
import type {
  TEmits as TTransferCardToMemberEmits,
  TProps as TTransferCardToMemberProps,
} from "@/components/CardsTable/CardTransferModal.vue";
import type {
  TEmits as TBinDeprecatedAttentionEmits,
  TProps as TBinDeprecatedAttentionProps,
} from "@/components/CardUpgradeDialog/CardUpgradeAlertDialog.vue";
import type {
  TEmits as TUpgradeCardModalEmits,
  TProps as TUpgradeCardModalProps,
} from "@/components/CardUpgradeDialog/CardUpgradeDialog.vue";
import type {
  TEmits as TUpgradeCardSuccessEmits,
  TProps as TUpgradeCardSuccessProps,
} from "@/components/CardUpgradeDialog/CardUpgradeSuccessDialog.vue";
import type { TEmits as TGetMobileAppBannerEmits } from "@/components/GetMobileAppBanner/GetMobileAppBanner.vue";
import type {
  TEmits as TTeamInviteEmits,
  TProps as TTeamInviteProps,
} from "@/components/Invation/InvationModal.vue";
import {
  TNotificationDetailsModalEmits,
  TNotificationDetailsModalProps,
} from "@/components/NotificationsV2/NotificationsDetailsModal/types";
import { TNotificationModalEmits } from "@/components/NotificationsV2/NotificationsModalV2/types";
import type {
  TEmits as TNotificationEmits,
  TProps as TNotificationProps,
} from "@/components/PersonalNotification/PersonalNotificationModal.vue";
import type {
  TEmits as TTransferCardFromMemberEmits,
  TProps as TTransferCardFromMemberProps,
} from "@/components/TransferFromMemberModal/TransferFromMemberModal.vue";
import type {
  TTransferModalEmits,
  TTransferModalProps,
} from "@/components/TransferModal/types";
import type {
  TEmits as IUser2FAProviderEmits,
  TProps as IUser2FAProviderProps,
} from "@/components/UserAuthProviderModal/UserAuthProviderModal.vue";
import type {
  TWithdrawalOfFundsModalEmits,
  TWithdrawalOfFundsModalProps,
} from "@/components/WithdrawalOfFundsModal/types";
import type {
  TEmits as TTotalRefundModalEmits,
  TProps as TTotalRefundModalProps,
} from "@/components/TotalRefundModal.vue";
import type {
  TEmits as TNotificationMoneyRequestDetailsEmits,
  TProps as TNotificationMoneyRequestDetailsProps,
} from "@/components/NotificationsV2/NotificationsMoneyRequestActive/NotificationsMoneyRequestActiveDetails.vue";
import type {
  TTransactionsModalEmits,
  TTransactionsModalProps,
} from "@/components/TransactionsModal/types";
import type {
  TPaymentDetailsModalEmits,
  TPaymentDetailsModalProps,
} from "@/components/PaymentDetailsModal/types";

export type TModalType = TModalActionsMap["type"];

export type ModalItem =
  | {
      type: "teamInvite";
      props: TTeamInviteProps;
      resolve: TModalResolver<"teamInvite">;
    }
  | {
      type: "personalNotification";
      props: TNotificationProps;
      resolve: TModalResolver<"personalNotification">;
    }
  | {
      type: "getMobileAppBanner";
      props: null;
      resolve: TModalResolver<"getMobileAppBanner">;
    }
  | {
      type: "cardDetailFull";
      props: TCardDetailFullProps;
      resolve: TModalResolver<"cardDetailFull">;
    }
  | {
      type: "cardRename";
      props: TCardRenameProps;
      resolve: TModalResolver<"cardRename">;
    }
  | {
      type: "transferCardToMember";
      props: TTransferCardToMemberProps;
      resolve: TModalResolver<"transferCardToMember">;
    }
  | {
      type: "transferCardFromMember";
      props: TTransferCardFromMemberProps;
      resolve: TModalResolver<"transferCardFromMember">;
    }
  | {
      type: "blockCard";
      props: TBlockCardProps;
      resolve: TModalResolver<"blockCard">;
    }
  | {
      type: "cardAutoRefill";
      props: TCardAutoRefillProps;
      resolve: TModalResolver<"cardAutoRefill">;
    }
  | {
      type: "upgradeCardModal";
      props: TUpgradeCardModalProps;
      resolve: TModalResolver<"upgradeCardModal">;
    }
  | {
      type: "upgradeCardSuccess";
      props: TUpgradeCardSuccessProps;
      resolve: TModalResolver<"upgradeCardSuccess">;
    }
  | {
      type: "binDeprecatedAttention";
      props: TBinDeprecatedAttentionProps;
      resolve: TModalResolver<"binDeprecatedAttention">;
    }
  | {
      type: "user2FAProvider";
      props: IUser2FAProviderProps;
      resolve: TModalResolver<"user2FAProvider">;
    }
  | {
      type: "supportSideModal";
      props: {};
      resolve: TModalResolver<"supportSideModal">;
    }
  | {
      type: "transferModal";
      props: TTransferModalProps;
      resolve: TModalResolver<"transferModal">;
    }
  | {
      type: "notificationDetailModal";
      props: TNotificationDetailsModalProps;
      resolve: TModalResolver<"notificationDetailModal">;
    }
  | {
      type: "systemNotification";
      props: {};
      resolve: TModalResolver<"systemNotification">;
    }
  | {
      type: "withdrawalOfFunds";
      props: TWithdrawalOfFundsModalProps;
      resolve: TModalResolver<"withdrawalOfFunds">;
    }
  | {
      type: "withdrawalToAccountForPstUser";
      props: {};
      resolve: TModalResolver<"withdrawalToAccountForPstUser">;
    }
  | {
      type: "withdrawalToWalletExternal";
      props: {};
      resolve: TModalResolver<"withdrawalToWalletExternal">;
    }
  | {
      type: "cardDetailsUnavailable";
      props: {};
      resolve: TModalResolver<"cardDetailsUnavailable">;
    }
  | {
      type: "totalRefund";
      props: TTotalRefundModalProps;
      resolve: TModalResolver<"totalRefund">;
    }
  | {
      type: "notificationMoneyRequestDetails";
      props: TNotificationMoneyRequestDetailsProps;
      resolve: TModalResolver<"notificationMoneyRequestDetails">;
    }
  | {
      type: "transactions";
      props: TTransactionsModalProps;
      resolve: TModalResolver<"transactions">;
    }
  | {
      type: "paymentDetails";
      props: TPaymentDetailsModalProps;
      resolve: TModalResolver<"paymentDetails">;
    };

export type TModalResolver<T extends TModalType = TModalType> = (
  value: ModalResponse<T> | Promise<ModalResponse<T>>
) => void;

export type TModalActionsMap =
  | {
      type: "teamInvite";
      actions: TransformEmits<TTeamInviteEmits>;
    }
  | {
      type: "personalNotification";
      actions: TransformEmits<TNotificationEmits>;
    }
  | {
      type: "getMobileAppBanner";
      actions: TransformEmits<TGetMobileAppBannerEmits>;
    }
  | {
      type: "cardDetailFull";
      actions: TransformEmits<TCardDetailFullEmits>;
    }
  | {
      type: "cardRename";
      actions: TransformEmits<TCardRenameEmits>;
    }
  | {
      type: "transferCardToMember";
      actions: TransformEmits<TTransferCardToMemberEmits>;
    }
  | {
      type: "transferCardFromMember";
      actions: TransformEmits<TTransferCardFromMemberEmits>;
    }
  | {
      type: "blockCard";
      actions: TransformEmits<TBlockCardEmits>;
    }
  | {
      type: "cardAutoRefill";
      actions: TransformEmits<TCardAutoRefillEmits>;
    }
  | {
      type: "upgradeCardModal";
      actions: TransformEmits<TUpgradeCardModalEmits>;
    }
  | {
      type: "upgradeCardSuccess";
      actions: TransformEmits<TUpgradeCardSuccessEmits>;
    }
  | {
      type: "binDeprecatedAttention";
      actions: TransformEmits<TBinDeprecatedAttentionEmits>;
    }
  | {
      type: "user2FAProvider";
      actions: TransformEmits<IUser2FAProviderEmits>;
    }
  | {
      type: "supportSideModal";
      actions: TransformEmits<{}>;
    }
  | {
      type: "transferModal";
      actions: TransformEmits<TTransferModalEmits>;
    }
  | {
      type: "notificationDetailModal";
      actions: TransformEmits<TNotificationDetailsModalEmits>;
    }
  | {
      type: "systemNotification";
      actions: TransformEmits<TNotificationModalEmits>;
    }
  | {
      type: "withdrawalOfFunds";
      actions: TransformEmits<TWithdrawalOfFundsModalEmits>;
    }
  | {
      type: "withdrawalToAccountForPstUser";
      actions: TransformEmits<{}>;
    }
  | {
      type: "withdrawalToWalletExternal";
      actions: TransformEmits<{}>;
    }
  | {
      type: "cardDetailsUnavailable";
      actions: TransformEmits<{}>;
    }
  | {
      type: "totalRefund";
      actions: TransformEmits<TTotalRefundModalEmits>;
    }
  | {
      type: "notificationMoneyRequestDetails";
      actions: TransformEmits<TNotificationMoneyRequestDetailsEmits>;
    }
  | {
      type: "transactions";
      actions: TransformEmits<TTransactionsModalEmits>;
    }
  | {
      type: "paymentDetails";
      actions: TransformEmits<TPaymentDetailsModalEmits>;
    };

export type ModalProps<T> = Extract<ModalItem, { type: T }>["props"];

export type ModalResponse<T extends TModalType = TModalType> = Extract<
  TModalActionsMap,
  { type: T }
>["actions"];

export type TransformEmits<T extends Record<string, any[]>> = {
  [K in keyof T]: T[K] extends [] ? { action: K } : { action: K } & T[K][0];
}[keyof T];
